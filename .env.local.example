# OpenAI API Key for Text-to-Speech
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Set a default voice for TTS (alloy, echo, fable, onyx, nova, shimmer)
DEFAULT_TTS_VOICE=alloy

# Database URL
DATABASE_URL="postgresql://username:password@localhost:5432/french_lesson?schema=public"

# Next Auth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here

# JWT Secret
JWT_SECRET=your_jwt_secret_here
