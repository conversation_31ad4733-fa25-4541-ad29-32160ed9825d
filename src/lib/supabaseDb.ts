import { getSupabaseClient, TABLES } from './supabase';
import type {
  User,
  <PERSON>on,
  LessonProgress,
  VocabularyItem,
  Conversation,
  ExamResult,
  Message
} from '../types/api';
import { hash, compare } from 'bcrypt';

// Get Supabase client (use admin for server-side operations)
const supabase = getSupabaseClient(true);

// User operations
export const findUserById = async (id: string): Promise<User | null> => {
  const { data: user, error } = await supabase
    .from(TABLES.USERS)
    .select('*')
    .eq('id', id)
    .single();

  if (error || !user) return null;

  return {
    id: user.id,
    name: user.name,
    email: user.email,
    level: user.level,
    points: user.points,
    streakDays: user.streakDays,
    joinedAt: user.joinedAt,
    learningGoals: user.learningGoals,
    completedLessons: user.completedLessons,
    lastActive: user.lastActive,
    preferences: {
      dailyGoal: user.dailyGoal,
      notifications: user.notifications,
      theme: user.theme as 'light' | 'dark',
    }
  };
};

export const findUserByEmail = async (email: string): Promise<User | null> => {
  const { data: user, error } = await supabase
    .from(TABLES.USERS)
    .select('*')
    .eq('email', email)
    .single();

  if (error || !user) return null;

  return {
    id: user.id,
    name: user.name,
    email: user.email,
    level: user.level,
    points: user.points,
    streakDays: user.streakDays,
    joinedAt: user.joinedAt,
    learningGoals: user.learningGoals,
    completedLessons: user.completedLessons,
    lastActive: user.lastActive,
    preferences: {
      dailyGoal: user.dailyGoal,
      notifications: user.notifications,
      theme: user.theme as 'light' | 'dark',
    }
  };
};

export const createUser = async (userData: Omit<User, 'id'> & { password: string }): Promise<User> => {
  const hashedPassword = await hash(userData.password, 10);

  const { data: user, error } = await supabase
    .from(TABLES.USERS)
    .insert({
      name: userData.name,
      email: userData.email,
      password: hashedPassword,
      level: userData.level,
      points: userData.points,
      streakDays: userData.streakDays,
      learningGoals: userData.learningGoals,
      completedLessons: userData.completedLessons,
      lastActive: userData.lastActive,
      dailyGoal: userData.preferences.dailyGoal,
      notifications: userData.preferences.notifications,
      theme: userData.preferences.theme,
      joinedAt: new Date().toISOString(),
    })
    .select()
    .single();

  if (error || !user) {
    throw new Error(`Failed to create user: ${error?.message}`);
  }

  return {
    id: user.id,
    name: user.name,
    email: user.email,
    level: user.level,
    points: user.points,
    streakDays: user.streakDays,
    joinedAt: user.joinedAt,
    learningGoals: user.learningGoals,
    completedLessons: user.completedLessons,
    lastActive: user.lastActive,
    preferences: {
      dailyGoal: user.dailyGoal,
      notifications: user.notifications,
      theme: user.theme as 'light' | 'dark',
    }
  };
};

export const updateUser = async (id: string, updates: Partial<User>): Promise<User | null> => {
  const updateData: any = {};
  
  if (updates.name) updateData.name = updates.name;
  if (updates.email) updateData.email = updates.email;
  if (updates.level) updateData.level = updates.level;
  if (updates.points !== undefined) updateData.points = updates.points;
  if (updates.streakDays !== undefined) updateData.streakDays = updates.streakDays;
  if (updates.learningGoals) updateData.learningGoals = updates.learningGoals;
  if (updates.completedLessons !== undefined) updateData.completedLessons = updates.completedLessons;
  if (updates.lastActive) updateData.lastActive = updates.lastActive;
  
  if (updates.preferences) {
    if (updates.preferences.dailyGoal !== undefined) updateData.dailyGoal = updates.preferences.dailyGoal;
    if (updates.preferences.notifications !== undefined) updateData.notifications = updates.preferences.notifications;
    if (updates.preferences.theme) updateData.theme = updates.preferences.theme;
  }

  const { data: user, error } = await supabase
    .from(TABLES.USERS)
    .update(updateData)
    .eq('id', id)
    .select()
    .single();

  if (error || !user) return null;

  return {
    id: user.id,
    name: user.name,
    email: user.email,
    level: user.level,
    points: user.points,
    streakDays: user.streakDays,
    joinedAt: user.joinedAt,
    learningGoals: user.learningGoals,
    completedLessons: user.completedLessons,
    lastActive: user.lastActive,
    preferences: {
      dailyGoal: user.dailyGoal,
      notifications: user.notifications,
      theme: user.theme as 'light' | 'dark',
    }
  };
};

export const verifyUserPassword = async (email: string, password: string): Promise<User | null> => {
  const { data: user, error } = await supabase
    .from(TABLES.USERS)
    .select('*')
    .eq('email', email)
    .single();

  if (error || !user || !user.password) return null;

  const isValidPassword = await compare(password, user.password);
  if (!isValidPassword) return null;

  return {
    id: user.id,
    name: user.name,
    email: user.email,
    level: user.level,
    points: user.points,
    streakDays: user.streakDays,
    joinedAt: user.joinedAt,
    learningGoals: user.learningGoals,
    completedLessons: user.completedLessons,
    lastActive: user.lastActive,
    preferences: {
      dailyGoal: user.dailyGoal,
      notifications: user.notifications,
      theme: user.theme as 'light' | 'dark',
    }
  };
};

// Lesson operations
export const getAllLessons = async (): Promise<Lesson[]> => {
  const { data: lessons, error } = await supabase
    .from(TABLES.LESSONS)
    .select(`
      *,
      sections:${TABLES.LESSON_SECTIONS}(*)
    `);

  if (error || !lessons) return [];

  return lessons.map((lesson: any) => ({
    id: lesson.id,
    title: lesson.title,
    description: lesson.description,
    level: lesson.level,
    duration: lesson.duration,
    topics: lesson.topics,
    sections: lesson.sections?.map((section: any) => ({
      id: section.id,
      lessonId: section.lessonId,
      title: section.title,
      type: section.type as 'text' | 'audio' | 'video' | 'image' | 'exercise',
      content: section.content || undefined,
      audioUrl: section.audioUrl || undefined,
      videoUrl: section.videoUrl || undefined,
      order: section.order,
      exercises: []
    })) || []
  }));
};

export const getLessonById = async (id: string): Promise<Lesson | null> => {
  const { data: lesson, error } = await supabase
    .from(TABLES.LESSONS)
    .select(`
      *,
      sections:${TABLES.LESSON_SECTIONS}(*)
    `)
    .eq('id', id)
    .single();

  if (error || !lesson) return null;

  return {
    id: lesson.id,
    title: lesson.title,
    description: lesson.description,
    level: lesson.level,
    duration: lesson.duration,
    topics: lesson.topics,
    sections: lesson.sections?.map((section: any) => ({
      id: section.id,
      lessonId: section.lessonId,
      title: section.title,
      type: section.type as 'text' | 'audio' | 'video' | 'image' | 'exercise',
      content: section.content || undefined,
      audioUrl: section.audioUrl || undefined,
      videoUrl: section.videoUrl || undefined,
      order: section.order,
      exercises: []
    })) || []
  };
};

export const getLessonProgress = async (userId: string, lessonId: string): Promise<LessonProgress | null> => {
  const { data: progress, error } = await supabase
    .from(TABLES.LESSON_PROGRESS)
    .select('*')
    .eq('userId', userId)
    .eq('lessonId', lessonId)
    .single();

  if (error || !progress) return null;

  return {
    id: progress.id,
    userId: progress.userId,
    lessonId: progress.lessonId,
    completed: progress.completed,
    score: progress.score,
    timeSpent: progress.timeSpent,
    completedAt: progress.completedAt,
  };
};

export const createLessonProgress = async (progressData: Omit<LessonProgress, 'id'>): Promise<LessonProgress> => {
  const { data: progress, error } = await supabase
    .from(TABLES.LESSON_PROGRESS)
    .insert(progressData)
    .select()
    .single();

  if (error || !progress) {
    throw new Error(`Failed to create lesson progress: ${error?.message}`);
  }

  return {
    id: progress.id,
    userId: progress.userId,
    lessonId: progress.lessonId,
    completed: progress.completed,
    score: progress.score,
    timeSpent: progress.timeSpent,
    completedAt: progress.completedAt,
  };
};

export const updateLessonProgress = async (id: string, updates: Partial<LessonProgress>): Promise<LessonProgress | null> => {
  const { data: progress, error } = await supabase
    .from(TABLES.LESSON_PROGRESS)
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error || !progress) return null;

  return {
    id: progress.id,
    userId: progress.userId,
    lessonId: progress.lessonId,
    completed: progress.completed,
    score: progress.score,
    timeSpent: progress.timeSpent,
    completedAt: progress.completedAt,
  };
};

// Vocabulary operations
export const getAllVocabulary = async (): Promise<VocabularyItem[]> => {
  const { data: vocabulary, error } = await supabase
    .from(TABLES.VOCABULARY)
    .select('*');

  if (error || !vocabulary) return [];

  return vocabulary.map((item: any) => ({
    id: item.id,
    french: item.french,
    english: item.english,
    pronunciation: item.pronunciation,
    category: item.category,
    difficulty: item.difficulty,
    audioUrl: item.audioUrl,
  }));
};

export const getVocabularyByCategory = async (category: string): Promise<VocabularyItem[]> => {
  const { data: vocabulary, error } = await supabase
    .from(TABLES.VOCABULARY)
    .select('*')
    .eq('category', category);

  if (error || !vocabulary) return [];

  return vocabulary.map((item: any) => ({
    id: item.id,
    french: item.french,
    english: item.english,
    pronunciation: item.pronunciation,
    category: item.category,
    difficulty: item.difficulty,
    audioUrl: item.audioUrl,
  }));
};

export const getUserVocabulary = async (userId: string): Promise<any[]> => {
  const { data: userVocabulary, error } = await supabase
    .from(TABLES.USER_VOCABULARY)
    .select(`
      *,
      vocabulary:${TABLES.VOCABULARY}(*)
    `)
    .eq('userId', userId)
    .order('lastPracticed', { ascending: false });

  if (error || !userVocabulary) return [];

  return userVocabulary;
};

// Conversation operations
export const getConversation = async (id: string): Promise<Conversation | null> => {
  const { data: conversation, error } = await supabase
    .from(TABLES.CONVERSATIONS)
    .select(`
      *,
      messages:${TABLES.MESSAGES}(*)
    `)
    .eq('id', id)
    .single();

  if (error || !conversation) return null;

  return {
    id: conversation.id,
    userId: conversation.userId,
    title: conversation.title,
    context: conversation.context,
    messages: conversation.messages.map((msg: any) => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
      timestamp: msg.timestamp,
    })),
    startedAt: conversation.startedAt,
    lastMessageAt: conversation.lastMessageAt,
  };
};

export const getUserConversations = async (userId: string): Promise<any[]> => {
  const { data: conversations, error } = await supabase
    .from(TABLES.CONVERSATIONS)
    .select(`
      *,
      messages:${TABLES.MESSAGES}(*)
    `)
    .eq('userId', userId)
    .order('lastMessageAt', { ascending: false });

  if (error || !conversations) return [];

  return conversations;
};

export const createConversation = async (userId: string, title: string, context: string, initialMessage?: string): Promise<Conversation> => {
  const now = new Date().toISOString();

  const { data: conversation, error } = await supabase
    .from(TABLES.CONVERSATIONS)
    .insert({
      userId,
      title,
      context,
      startedAt: now,
      lastMessageAt: now,
    })
    .select()
    .single();

  if (error || !conversation) {
    throw new Error(`Failed to create conversation: ${error?.message}`);
  }

  // Add initial message if provided
  if (initialMessage) {
    await supabase
      .from(TABLES.MESSAGES)
      .insert({
        conversationId: conversation.id,
        role: 'user',
        content: initialMessage,
        timestamp: now,
      });
  }

  return {
    id: conversation.id,
    userId: conversation.userId,
    title: conversation.title,
    context: conversation.context,
    messages: initialMessage ? [{
      role: 'user' as const,
      content: initialMessage,
      timestamp: now,
    }] : [],
    startedAt: conversation.startedAt,
    lastMessageAt: conversation.lastMessageAt,
  };
};

export const addMessageToConversation = async (conversationId: string, message: Omit<Message, 'timestamp'>): Promise<Conversation | null> => {
  const now = new Date().toISOString();

  // First check if conversation exists
  const { data: conversationExists, error: checkError } = await supabase
    .from(TABLES.CONVERSATIONS)
    .select('id')
    .eq('id', conversationId)
    .single();

  if (checkError || !conversationExists) return null;

  // Add message
  const { error: messageError } = await supabase
    .from(TABLES.MESSAGES)
    .insert({
      conversationId,
      role: message.role,
      content: message.content,
      timestamp: now,
    });

  if (messageError) return null;

  // Update conversation lastMessageAt
  const { error: updateError } = await supabase
    .from(TABLES.CONVERSATIONS)
    .update({ lastMessageAt: now })
    .eq('id', conversationId);

  if (updateError) return null;

  // Get updated conversation
  return await getConversation(conversationId);
};

// Exam operations
export const createExamResult = async (examData: Omit<ExamResult, 'id'>): Promise<ExamResult> => {
  const { data: examResult, error } = await supabase
    .from(TABLES.EXAM_RESULTS)
    .insert(examData)
    .select()
    .single();

  if (error || !examResult) {
    throw new Error(`Failed to create exam result: ${error?.message}`);
  }

  return {
    id: examResult.id,
    userId: examResult.userId,
    examType: examResult.examType,
    score: examResult.score,
    totalQuestions: examResult.totalQuestions,
    correctAnswers: examResult.correctAnswers,
    timeSpent: examResult.timeSpent,
    completedAt: examResult.completedAt,
    answers: examResult.answers,
  };
};

export const getUserExamResults = async (userId: string): Promise<ExamResult[]> => {
  const { data: examResults, error } = await supabase
    .from(TABLES.EXAM_RESULTS)
    .select('*')
    .eq('userId', userId)
    .order('completedAt', { ascending: false });

  if (error || !examResults) return [];

  return examResults.map((result: any) => ({
    id: result.id,
    userId: result.userId,
    examType: result.examType,
    score: result.score,
    totalQuestions: result.totalQuestions,
    correctAnswers: result.correctAnswers,
    timeSpent: result.timeSpent,
    completedAt: result.completedAt,
    answers: result.answers,
  }));
};
