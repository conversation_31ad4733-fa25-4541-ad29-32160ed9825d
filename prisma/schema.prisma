generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                        String              @id @default(uuid())
  email                     String              @unique
  name                      String
  password                  String?
  level                     String              @default("A1")
  points                    Int                 @default(0)
  streakDays                Int                 @default(0)
  joinedAt                  DateTime            @default(now())
  learningGoals             String[]            @default([])
  completedLessons          Int                 @default(0)
  lastActive                DateTime            @default(now())
  dailyGoal                 Int                 @default(15)
  notifications             <PERSON>olean             @default(true)
  theme                     String              @default("light")
  aiCorrectionEnabled       Boolean             @default(true)
  aiVocabSuggestionsEnabled Boolean             @default(true)
  preferredVoice            String?
  speechRecognitionEnabled  Boolean             @default(true)
  conversations             Conversation[]
  examResults               ExamResult[]
  lessonProgress            LessonProgress[]
  practiceSessions          PracticeSession[]
  templateUsage             UserTemplateUsage[]
  vocabulary                UserVocabulary[]
}

model Lesson {
  id          String           @id @default(uuid())
  title       String
  description String
  level       String
  duration    Int
  topics      String[]
  progress    LessonProgress[]
  sections    LessonSection[]
}

model LessonSection {
  id        String           @id @default(uuid())
  lessonId  String
  title     String
  type      String
  content   String?
  audioUrl  String?
  videoUrl  String?
  order     Int
  exercises LessonExercise[]
  lesson    Lesson           @relation(fields: [lessonId], references: [id])
}

model LessonExercise {
  id            String        @id @default(uuid())
  sectionId     String
  type          String
  question      String
  options       String[]
  correctAnswer String
  explanation   String?
  section       LessonSection @relation(fields: [sectionId], references: [id])
}

model LessonProgress {
  id          String    @id @default(uuid())
  lessonId    String
  userId      String
  completed   Boolean   @default(false)
  score       Int       @default(0)
  startedAt   DateTime?
  completedAt DateTime?
  answers     Json?
  lesson      Lesson    @relation(fields: [lessonId], references: [id])
  user        User      @relation(fields: [userId], references: [id])

  @@unique([userId, lessonId])
}

model Vocabulary {
  id             String           @id @default(uuid())
  word           String           @unique
  translation    String
  example        String
  level          String
  category       String?
  pronunciation  String?
  usageContext   String[]         @default([])
  practiceItems  PracticeItem[]
  userVocabulary UserVocabulary[]
}

model UserVocabulary {
  id              String     @id @default(uuid())
  userId          String
  vocabularyId    String
  learned         Boolean    @default(false)
  lastPracticed   DateTime?
  nextReviewDate  DateTime?
  repetitionStage Int        @default(0)
  user            User       @relation(fields: [userId], references: [id])
  vocabulary      Vocabulary @relation(fields: [vocabularyId], references: [id])

  @@unique([userId, vocabularyId])
}

model Conversation {
  id            String                @id @default(uuid())
  userId        String
  title         String
  context       String
  startedAt     DateTime              @default(now())
  lastMessageAt DateTime              @default(now())
  templateId    String?
  template      ConversationTemplate? @relation(fields: [templateId], references: [id])
  user          User                  @relation(fields: [userId], references: [id])
  messages      Message[]
}

model Message {
  id                  String       @id @default(uuid())
  conversationId      String
  role                String
  content             String
  timestamp           DateTime     @default(now())
  audioUrl            String?
  corrections         Json?
  suggestedVocabulary Json?
  conversation        Conversation @relation(fields: [conversationId], references: [id])
}

model ConversationTemplate {
  id                String              @id @default(uuid())
  title             String
  description       String
  systemPrompt      String
  initialMessage    String
  topics            String[]
  level             String
  conversations     Conversation[]
  userTemplateUsage UserTemplateUsage[]
}

model UserTemplateUsage {
  id         String               @id @default(uuid())
  userId     String
  templateId String
  usedAt     DateTime             @default(now())
  template   ConversationTemplate @relation(fields: [templateId], references: [id])
  user       User                 @relation(fields: [userId], references: [id])
}

model ExamResult {
  id          String   @id @default(uuid())
  userId      String
  examId      String
  section     String
  level       String
  score       Int
  details     Json
  completedAt DateTime
  timeSpent   Int
  user        User     @relation(fields: [userId], references: [id])
}

model PracticeSession {
  id                 String                      @id @default(uuid())
  userId             String
  type               String
  duration           Int
  createdAt          DateTime                    @default(now())
  aiGenerated        Boolean                     @default(false)
  difficulty         String?
  score              Int?
  practiceItems      PracticeItem[]
  user               User                        @relation(fields: [userId], references: [id])
  pronunciationItems PronunciationPracticeItem[]
}

model PracticeItem {
  id             String          @id @default(uuid())
  sessionId      String
  vocabularyId   String
  exerciseType   String
  isCorrect      Boolean
  userAnswer     String
  expectedAnswer String
  session        PracticeSession @relation(fields: [sessionId], references: [id])
  vocabulary     Vocabulary      @relation(fields: [vocabularyId], references: [id])
}

model PronunciationExercise {
  id                    String                      @id @default(uuid())
  text                  String
  translation           String?
  difficulty            String
  category              String?
  expectedPronunciation String?
  practiceItems         PronunciationPracticeItem[]
}

model PronunciationPracticeItem {
  id              String                @id @default(uuid())
  sessionId       String
  exerciseId      String
  userAudioUrl    String?
  transcript      String?
  similarityScore Float?
  feedback        Json?
  exercise        PronunciationExercise @relation(fields: [exerciseId], references: [id])
  session         PracticeSession       @relation(fields: [sessionId], references: [id])
}

model GrammarRule {
  id          String   @id @default(uuid())
  title       String
  description String
  examples    String[]
  level       String
  category    String
}
