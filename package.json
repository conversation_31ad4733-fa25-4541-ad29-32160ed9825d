{"name": "french-tutor-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:seed": "node prisma/seed/index.js", "db:setup": "prisma generate && prisma migrate dev && npm run db:seed", "generate-vocabulary": "node scripts/generate-vocabulary.js", "seed-vocabulary": "node scripts/seed-vocabulary.js", "add-vocabulary": "node scripts/add-vocabulary.js", "check-vocabulary": "node scripts/check-vocabulary.js", "add-lessons": "node scripts/add-lessons.js", "check-lessons": "node scripts/check-lessons.js"}, "prisma": {"seed": "node prisma/seed/index.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^6.6.0", "@reduxjs/toolkit": "^2.0.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/bcrypt": "^5.0.2", "@types/formidable": "^3.4.5", "autoprefixer": "^10.4.0", "axios": "^1.6.0", "bcrypt": "^5.1.1", "buffer": "^6.0.3", "csv-parse": "^5.6.0", "form-data": "^4.0.2", "formidable": "^3.5.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.503.0", "next": "^14.0.0", "next-auth": "^4.24.11", "nookies": "^2.5.2", "openai": "^4.96.0", "postcss": "^8.4.0", "prisma": "^6.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.0", "sonner": "^2.0.3", "tailwindcss": "^3.3.0"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.50.0", "eslint-config-next": "^14.0.0", "ts-node": "^10.9.2", "typescript": "^5.2.0"}}