# DATABASE_URL="postgresql://postgres.ghvnvbbwouzauawmabub:#<EMAIL>:5432/postgres"
# DATABASE_URL="postgresql://postgres:admin@localhost:5432/french_lesson?schema=public"
JWT_SECRET="secret$-so_complicated"
OPENAI_API_KEY="********************************************************************************************************************************************************************"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"
# Optional: Set a default voice for TTS (alloy, echo, fable, onyx, nova, shimmer)
DEFAULT_TTS_VOICE="alloy"
NEXTAUTH_SECRET="your_nextauth_secret_here"

# Connect to Supabase via connection pooling
DATABASE_URL="postgresql://postgres.fvhmdgbqjoshcdbqyitx:#<EMAIL>:6543/postgres?pgbouncer=true"

# Direct connection to the database. Used for migrations
DIRECT_URL="postgresql://postgres.fvhmdgbqjoshcdbqyitx:#<EMAIL>:5432/postgres"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://fvhmdgbqjoshcdbqyitx.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ2aG1kZ2Jxam9zaGNkYnF5aXR4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzE5NzQsImV4cCI6MjA1MDU0Nzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"
SUPABASE_SERVICE_ROLE_KEY="your_service_role_key_here"