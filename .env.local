# DATABASE_URL="postgresql://postgres.ghvnvbbwouzauawmabub:#<EMAIL>:5432/postgres"
# DATABASE_URL="postgresql://postgres:admin@localhost:5432/french_lesson?schema=public"
JWT_SECRET="secret$-so_complicated"
OPENAI_API_KEY="********************************************************************************************************************************************************************"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"
# Optional: Set a default voice for TTS (alloy, echo, fable, onyx, nova, shimmer)
DEFAULT_TTS_VOICE="alloy"
NEXTAUTH_SECRET="your_nextauth_secret_here"

# Connect to Supabase via connection pooling
DATABASE_URL="postgresql://postgres.fvhmdgbqjoshcdbqyitx:#<EMAIL>:6543/postgres?pgbouncer=true"

# Direct connection to the database. Used for migrations
DIRECT_URL="postgresql://postgres.fvhmdgbqjoshcdbqyitx:#<EMAIL>:5432/postgres"