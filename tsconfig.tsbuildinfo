{"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.es2021.d.ts", "./node_modules/typescript/lib/lib.es2022.d.ts", "./node_modules/typescript/lib/lib.es2023.d.ts", "./node_modules/typescript/lib/lib.es2024.d.ts", "./node_modules/typescript/lib/lib.esnext.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2016.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.es2021.promise.d.ts", "./node_modules/typescript/lib/lib.es2021.string.d.ts", "./node_modules/typescript/lib/lib.es2021.weakref.d.ts", "./node_modules/typescript/lib/lib.es2021.intl.d.ts", "./node_modules/typescript/lib/lib.es2022.array.d.ts", "./node_modules/typescript/lib/lib.es2022.error.d.ts", "./node_modules/typescript/lib/lib.es2022.intl.d.ts", "./node_modules/typescript/lib/lib.es2022.object.d.ts", "./node_modules/typescript/lib/lib.es2022.string.d.ts", "./node_modules/typescript/lib/lib.es2022.regexp.d.ts", "./node_modules/typescript/lib/lib.es2023.array.d.ts", "./node_modules/typescript/lib/lib.es2023.collection.d.ts", "./node_modules/typescript/lib/lib.es2023.intl.d.ts", "./node_modules/typescript/lib/lib.es2024.arraybuffer.d.ts", "./node_modules/typescript/lib/lib.es2024.collection.d.ts", "./node_modules/typescript/lib/lib.es2024.object.d.ts", "./node_modules/typescript/lib/lib.es2024.promise.d.ts", "./node_modules/typescript/lib/lib.es2024.regexp.d.ts", "./node_modules/typescript/lib/lib.es2024.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2024.string.d.ts", "./node_modules/typescript/lib/lib.esnext.array.d.ts", "./node_modules/typescript/lib/lib.esnext.collection.d.ts", "./node_modules/typescript/lib/lib.esnext.intl.d.ts", "./node_modules/typescript/lib/lib.esnext.disposable.d.ts", "./node_modules/typescript/lib/lib.esnext.promise.d.ts", "./node_modules/typescript/lib/lib.esnext.decorators.d.ts", "./node_modules/typescript/lib/lib.esnext.iterator.d.ts", "./node_modules/typescript/lib/lib.esnext.float16.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/next/dist/styled-jsx/types/css.d.ts", "./node_modules/@types/react/global.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@types/prop-types/index.d.ts", "./node_modules/@types/react/index.d.ts", "./node_modules/next/dist/styled-jsx/types/index.d.ts", "./node_modules/next/dist/styled-jsx/types/macro.d.ts", "./node_modules/next/dist/styled-jsx/types/style.d.ts", "./node_modules/next/dist/styled-jsx/types/global.d.ts", "./node_modules/next/dist/shared/lib/amp.d.ts", "./node_modules/next/amp.d.ts", "./node_modules/@types/node/compatibility/disposable.d.ts", "./node_modules/@types/node/compatibility/indexable.d.ts", "./node_modules/@types/node/compatibility/iterators.d.ts", "./node_modules/@types/node/compatibility/index.d.ts", "./node_modules/@types/node/globals.typedarray.d.ts", "./node_modules/@types/node/buffer.buffer.d.ts", "./node_modules/buffer/index.d.ts", "./node_modules/undici-types/header.d.ts", "./node_modules/undici-types/readable.d.ts", "./node_modules/undici-types/file.d.ts", "./node_modules/undici-types/fetch.d.ts", "./node_modules/undici-types/formdata.d.ts", "./node_modules/undici-types/connector.d.ts", "./node_modules/undici-types/client.d.ts", "./node_modules/undici-types/errors.d.ts", "./node_modules/undici-types/dispatcher.d.ts", "./node_modules/undici-types/global-dispatcher.d.ts", "./node_modules/undici-types/global-origin.d.ts", "./node_modules/undici-types/pool-stats.d.ts", "./node_modules/undici-types/pool.d.ts", "./node_modules/undici-types/handlers.d.ts", "./node_modules/undici-types/balanced-pool.d.ts", "./node_modules/undici-types/agent.d.ts", "./node_modules/undici-types/mock-interceptor.d.ts", "./node_modules/undici-types/mock-agent.d.ts", "./node_modules/undici-types/mock-client.d.ts", "./node_modules/undici-types/mock-pool.d.ts", "./node_modules/undici-types/mock-errors.d.ts", "./node_modules/undici-types/proxy-agent.d.ts", "./node_modules/undici-types/env-http-proxy-agent.d.ts", "./node_modules/undici-types/retry-handler.d.ts", "./node_modules/undici-types/retry-agent.d.ts", "./node_modules/undici-types/api.d.ts", "./node_modules/undici-types/interceptors.d.ts", "./node_modules/undici-types/util.d.ts", "./node_modules/undici-types/cookies.d.ts", "./node_modules/undici-types/patch.d.ts", "./node_modules/undici-types/websocket.d.ts", "./node_modules/undici-types/eventsource.d.ts", "./node_modules/undici-types/filereader.d.ts", "./node_modules/undici-types/diagnostics-channel.d.ts", "./node_modules/undici-types/content-type.d.ts", "./node_modules/undici-types/cache.d.ts", "./node_modules/undici-types/index.d.ts", "./node_modules/@types/node/globals.d.ts", "./node_modules/@types/node/assert.d.ts", "./node_modules/@types/node/assert/strict.d.ts", "./node_modules/@types/node/async_hooks.d.ts", "./node_modules/@types/node/buffer.d.ts", "./node_modules/@types/node/child_process.d.ts", "./node_modules/@types/node/cluster.d.ts", "./node_modules/@types/node/console.d.ts", "./node_modules/@types/node/constants.d.ts", "./node_modules/@types/node/crypto.d.ts", "./node_modules/@types/node/dgram.d.ts", "./node_modules/@types/node/diagnostics_channel.d.ts", "./node_modules/@types/node/dns.d.ts", "./node_modules/@types/node/dns/promises.d.ts", "./node_modules/@types/node/domain.d.ts", "./node_modules/@types/node/dom-events.d.ts", "./node_modules/@types/node/events.d.ts", "./node_modules/@types/node/fs.d.ts", "./node_modules/@types/node/fs/promises.d.ts", "./node_modules/@types/node/http.d.ts", "./node_modules/@types/node/http2.d.ts", "./node_modules/@types/node/https.d.ts", "./node_modules/@types/node/inspector.d.ts", "./node_modules/@types/node/module.d.ts", "./node_modules/@types/node/net.d.ts", "./node_modules/@types/node/os.d.ts", "./node_modules/@types/node/path.d.ts", "./node_modules/@types/node/perf_hooks.d.ts", "./node_modules/@types/node/process.d.ts", "./node_modules/@types/node/punycode.d.ts", "./node_modules/@types/node/querystring.d.ts", "./node_modules/@types/node/readline.d.ts", "./node_modules/@types/node/readline/promises.d.ts", "./node_modules/@types/node/repl.d.ts", "./node_modules/@types/node/sea.d.ts", "./node_modules/@types/node/stream.d.ts", "./node_modules/@types/node/stream/promises.d.ts", "./node_modules/@types/node/stream/consumers.d.ts", "./node_modules/@types/node/stream/web.d.ts", "./node_modules/@types/node/string_decoder.d.ts", "./node_modules/@types/node/test.d.ts", "./node_modules/@types/node/timers.d.ts", "./node_modules/@types/node/timers/promises.d.ts", "./node_modules/@types/node/tls.d.ts", "./node_modules/@types/node/trace_events.d.ts", "./node_modules/@types/node/tty.d.ts", "./node_modules/@types/node/url.d.ts", "./node_modules/@types/node/util.d.ts", "./node_modules/@types/node/v8.d.ts", "./node_modules/@types/node/vm.d.ts", "./node_modules/@types/node/wasi.d.ts", "./node_modules/@types/node/worker_threads.d.ts", "./node_modules/@types/node/zlib.d.ts", "./node_modules/@types/node/index.d.ts", "./node_modules/next/dist/server/get-page-files.d.ts", "./node_modules/@types/react/canary.d.ts", "./node_modules/@types/react/experimental.d.ts", "./node_modules/@types/react-dom/index.d.ts", "./node_modules/@types/react-dom/canary.d.ts", "./node_modules/@types/react-dom/experimental.d.ts", "./node_modules/next/dist/compiled/webpack/webpack.d.ts", "./node_modules/next/dist/server/config.d.ts", "./node_modules/next/dist/lib/load-custom-routes.d.ts", "./node_modules/next/dist/shared/lib/image-config.d.ts", "./node_modules/next/dist/build/webpack/plugins/subresource-integrity-plugin.d.ts", "./node_modules/next/dist/server/body-streams.d.ts", "./node_modules/next/dist/server/future/route-kind.d.ts", "./node_modules/next/dist/server/future/route-definitions/route-definition.d.ts", "./node_modules/next/dist/server/future/route-matches/route-match.d.ts", "./node_modules/next/dist/client/components/app-router-headers.d.ts", "./node_modules/next/dist/server/request-meta.d.ts", "./node_modules/next/dist/server/lib/revalidate.d.ts", "./node_modules/next/dist/server/config-shared.d.ts", "./node_modules/next/dist/server/base-http/index.d.ts", "./node_modules/next/dist/server/api-utils/index.d.ts", "./node_modules/next/dist/server/node-environment.d.ts", "./node_modules/next/dist/server/require-hook.d.ts", "./node_modules/next/dist/server/node-polyfill-crypto.d.ts", "./node_modules/next/dist/lib/page-types.d.ts", "./node_modules/next/dist/build/analysis/get-page-static-info.d.ts", "./node_modules/next/dist/build/webpack/loaders/get-module-build-info.d.ts", "./node_modules/next/dist/build/webpack/plugins/middleware-plugin.d.ts", "./node_modules/next/dist/server/render-result.d.ts", "./node_modules/next/dist/server/future/helpers/i18n-provider.d.ts", "./node_modules/next/dist/server/web/next-url.d.ts", "./node_modules/next/dist/compiled/@edge-runtime/cookies/index.d.ts", "./node_modules/next/dist/server/web/spec-extension/cookies.d.ts", "./node_modules/next/dist/server/web/spec-extension/request.d.ts", "./node_modules/next/dist/server/web/spec-extension/fetch-event.d.ts", "./node_modules/next/dist/server/web/spec-extension/response.d.ts", "./node_modules/next/dist/server/web/types.d.ts", "./node_modules/next/dist/lib/setup-exception-listeners.d.ts", "./node_modules/next/dist/lib/constants.d.ts", "./node_modules/next/dist/build/index.d.ts", "./node_modules/next/dist/build/webpack/plugins/pages-manifest-plugin.d.ts", "./node_modules/next/dist/shared/lib/router/utils/route-regex.d.ts", "./node_modules/next/dist/shared/lib/router/utils/route-matcher.d.ts", "./node_modules/next/dist/shared/lib/router/utils/parse-url.d.ts", "./node_modules/next/dist/server/base-http/node.d.ts", "./node_modules/next/dist/server/font-utils.d.ts", "./node_modules/next/dist/build/webpack/plugins/flight-manifest-plugin.d.ts", "./node_modules/next/dist/server/future/route-modules/route-module.d.ts", "./node_modules/next/dist/shared/lib/deep-readonly.d.ts", "./node_modules/next/dist/server/load-components.d.ts", "./node_modules/next/dist/shared/lib/router/utils/middleware-route-matcher.d.ts", "./node_modules/next/dist/build/webpack/plugins/next-font-manifest-plugin.d.ts", "./node_modules/next/dist/server/future/route-definitions/locale-route-definition.d.ts", "./node_modules/next/dist/server/future/route-definitions/pages-route-definition.d.ts", "./node_modules/next/dist/shared/lib/mitt.d.ts", "./node_modules/next/dist/client/with-router.d.ts", "./node_modules/next/dist/client/router.d.ts", "./node_modules/next/dist/client/route-loader.d.ts", "./node_modules/next/dist/client/page-loader.d.ts", "./node_modules/next/dist/shared/lib/bloom-filter.d.ts", "./node_modules/next/dist/shared/lib/router/router.d.ts", "./node_modules/next/dist/shared/lib/router-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/loadable.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.d.ts", "./node_modules/next/dist/server/future/route-definitions/app-page-route-definition.d.ts", "./node_modules/next/dist/shared/lib/modern-browserslist-target.d.ts", "./node_modules/next/dist/shared/lib/constants.d.ts", "./node_modules/next/dist/build/webpack/loaders/metadata/types.d.ts", "./node_modules/next/dist/build/page-extensions-type.d.ts", "./node_modules/next/dist/build/webpack/loaders/next-app-loader.d.ts", "./node_modules/next/dist/server/lib/app-dir-module.d.ts", "./node_modules/next/dist/server/response-cache/types.d.ts", "./node_modules/next/dist/server/response-cache/index.d.ts", "./node_modules/next/dist/server/lib/incremental-cache/index.d.ts", "./node_modules/next/dist/client/components/hooks-server-context.d.ts", "./node_modules/next/dist/server/app-render/dynamic-rendering.d.ts", "./node_modules/next/dist/client/components/static-generation-async-storage-instance.d.ts", "./node_modules/next/dist/client/components/static-generation-async-storage.external.d.ts", "./node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.d.ts", "./node_modules/next/dist/server/async-storage/draft-mode-provider.d.ts", "./node_modules/next/dist/server/web/spec-extension/adapters/headers.d.ts", "./node_modules/next/dist/client/components/request-async-storage-instance.d.ts", "./node_modules/next/dist/client/components/request-async-storage.external.d.ts", "./node_modules/next/dist/server/app-render/create-error-handler.d.ts", "./node_modules/next/dist/server/app-render/app-render.d.ts", "./node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.d.ts", "./node_modules/next/dist/shared/lib/amp-context.shared-runtime.d.ts", "./node_modules/next/dist/server/future/route-modules/app-page/vendored/contexts/entrypoints.d.ts", "./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.d.ts", "./node_modules/@types/react/jsx-runtime.d.ts", "./node_modules/next/dist/client/components/error-boundary.d.ts", "./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.d.ts", "./node_modules/next/dist/client/components/app-router.d.ts", "./node_modules/next/dist/client/components/layout-router.d.ts", "./node_modules/next/dist/client/components/render-from-template-context.d.ts", "./node_modules/next/dist/client/components/action-async-storage-instance.d.ts", "./node_modules/next/dist/client/components/action-async-storage.external.d.ts", "./node_modules/next/dist/client/components/client-page.d.ts", "./node_modules/next/dist/client/components/search-params.d.ts", "./node_modules/next/dist/client/components/not-found-boundary.d.ts", "./node_modules/next/dist/server/app-render/rsc/preloads.d.ts", "./node_modules/next/dist/server/app-render/rsc/postpone.d.ts", "./node_modules/next/dist/server/app-render/rsc/taint.d.ts", "./node_modules/next/dist/server/app-render/entry-base.d.ts", "./node_modules/next/dist/build/templates/app-page.d.ts", "./node_modules/next/dist/server/future/route-modules/app-page/module.d.ts", "./node_modules/next/dist/server/lib/builtin-request-context.d.ts", "./node_modules/next/dist/server/app-render/types.d.ts", "./node_modules/next/dist/client/components/router-reducer/fetch-server-response.d.ts", "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.d.ts", "./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.d.ts", "./node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/entrypoints.d.ts", "./node_modules/next/dist/server/future/route-modules/pages/module.compiled.d.ts", "./node_modules/next/dist/build/templates/pages.d.ts", "./node_modules/next/dist/server/future/route-modules/pages/module.d.ts", "./node_modules/next/dist/server/render.d.ts", "./node_modules/next/dist/server/future/route-definitions/pages-api-route-definition.d.ts", "./node_modules/next/dist/server/future/route-matches/pages-api-route-match.d.ts", "./node_modules/next/dist/server/future/route-matchers/route-matcher.d.ts", "./node_modules/next/dist/server/future/route-matcher-providers/route-matcher-provider.d.ts", "./node_modules/next/dist/server/future/route-matcher-managers/route-matcher-manager.d.ts", "./node_modules/next/dist/server/future/normalizers/normalizer.d.ts", "./node_modules/next/dist/server/future/normalizers/locale-route-normalizer.d.ts", "./node_modules/next/dist/server/future/normalizers/request/pathname-normalizer.d.ts", "./node_modules/next/dist/server/future/normalizers/request/suffix.d.ts", "./node_modules/next/dist/server/future/normalizers/request/rsc.d.ts", "./node_modules/next/dist/server/future/normalizers/request/prefix.d.ts", "./node_modules/next/dist/server/future/normalizers/request/postponed.d.ts", "./node_modules/next/dist/server/future/normalizers/request/action.d.ts", "./node_modules/next/dist/server/future/normalizers/request/prefetch-rsc.d.ts", "./node_modules/next/dist/server/future/normalizers/request/next-data.d.ts", "./node_modules/next/dist/server/base-server.d.ts", "./node_modules/next/dist/server/image-optimizer.d.ts", "./node_modules/next/dist/server/next-server.d.ts", "./node_modules/next/dist/lib/coalesced-function.d.ts", "./node_modules/next/dist/server/lib/router-utils/types.d.ts", "./node_modules/next/dist/trace/types.d.ts", "./node_modules/next/dist/trace/trace.d.ts", "./node_modules/next/dist/trace/shared.d.ts", "./node_modules/next/dist/trace/index.d.ts", "./node_modules/next/dist/build/load-jsconfig.d.ts", "./node_modules/next/dist/build/webpack-config.d.ts", "./node_modules/next/dist/build/webpack/plugins/define-env-plugin.d.ts", "./node_modules/next/dist/build/swc/index.d.ts", "./node_modules/next/dist/server/dev/parse-version-info.d.ts", "./node_modules/next/dist/server/dev/hot-reloader-types.d.ts", "./node_modules/next/dist/telemetry/storage.d.ts", "./node_modules/next/dist/server/lib/types.d.ts", "./node_modules/next/dist/server/lib/render-server.d.ts", "./node_modules/next/dist/server/lib/router-server.d.ts", "./node_modules/next/dist/shared/lib/router/utils/path-match.d.ts", "./node_modules/next/dist/server/lib/router-utils/filesystem.d.ts", "./node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.d.ts", "./node_modules/next/dist/server/lib/dev-bundler-service.d.ts", "./node_modules/next/dist/server/dev/static-paths-worker.d.ts", "./node_modules/next/dist/server/dev/next-dev-server.d.ts", "./node_modules/next/dist/server/next.d.ts", "./node_modules/next/dist/lib/metadata/types/alternative-urls-types.d.ts", "./node_modules/next/dist/lib/metadata/types/extra-types.d.ts", "./node_modules/next/dist/lib/metadata/types/metadata-types.d.ts", "./node_modules/next/dist/lib/metadata/types/manifest-types.d.ts", "./node_modules/next/dist/lib/metadata/types/opengraph-types.d.ts", "./node_modules/next/dist/lib/metadata/types/twitter-types.d.ts", "./node_modules/next/dist/lib/metadata/types/metadata-interface.d.ts", "./node_modules/next/types/index.d.ts", "./node_modules/next/dist/shared/lib/html-context.shared-runtime.d.ts", "./node_modules/@next/env/dist/index.d.ts", "./node_modules/next/dist/shared/lib/utils.d.ts", "./node_modules/next/dist/pages/_app.d.ts", "./node_modules/next/app.d.ts", "./node_modules/next/dist/server/web/spec-extension/unstable-cache.d.ts", "./node_modules/next/dist/server/web/spec-extension/revalidate.d.ts", "./node_modules/next/dist/server/web/spec-extension/unstable-no-store.d.ts", "./node_modules/next/cache.d.ts", "./node_modules/next/dist/shared/lib/runtime-config.external.d.ts", "./node_modules/next/config.d.ts", "./node_modules/next/dist/pages/_document.d.ts", "./node_modules/next/document.d.ts", "./node_modules/next/dist/shared/lib/dynamic.d.ts", "./node_modules/next/dynamic.d.ts", "./node_modules/next/dist/pages/_error.d.ts", "./node_modules/next/error.d.ts", "./node_modules/next/dist/shared/lib/head.d.ts", "./node_modules/next/head.d.ts", "./node_modules/next/dist/client/components/draft-mode.d.ts", "./node_modules/next/dist/client/components/headers.d.ts", "./node_modules/next/headers.d.ts", "./node_modules/next/dist/shared/lib/get-img-props.d.ts", "./node_modules/next/dist/client/image-component.d.ts", "./node_modules/next/dist/shared/lib/image-external.d.ts", "./node_modules/next/image.d.ts", "./node_modules/next/dist/client/link.d.ts", "./node_modules/next/link.d.ts", "./node_modules/next/dist/client/components/redirect-status-code.d.ts", "./node_modules/next/dist/client/components/redirect.d.ts", "./node_modules/next/dist/client/components/not-found.d.ts", "./node_modules/next/dist/client/components/navigation.react-server.d.ts", "./node_modules/next/dist/client/components/navigation.d.ts", "./node_modules/next/navigation.d.ts", "./node_modules/next/router.d.ts", "./node_modules/next/dist/client/script.d.ts", "./node_modules/next/script.d.ts", "./node_modules/next/dist/server/web/spec-extension/user-agent.d.ts", "./node_modules/next/dist/compiled/@edge-runtime/primitives/url.d.ts", "./node_modules/next/dist/server/web/spec-extension/image-response.d.ts", "./node_modules/next/dist/compiled/@vercel/og/satori/index.d.ts", "./node_modules/next/dist/compiled/@vercel/og/emoji/index.d.ts", "./node_modules/next/dist/compiled/@vercel/og/types.d.ts", "./node_modules/next/server.d.ts", "./node_modules/next/types/global.d.ts", "./node_modules/next/types/compiled.d.ts", "./node_modules/next/index.d.ts", "./node_modules/next/image-types/global.d.ts", "./next-env.d.ts", "./node_modules/@prisma/client/runtime/library.d.ts", "./node_modules/.prisma/client/default.d.ts", "./node_modules/@prisma/client/default.d.ts", "./prisma/seed/conversationtemplates.ts", "./prisma/seed/grammarrules.ts", "./prisma/seed/vocabulary.ts", "./prisma/seed/pronunciationexercises.ts", "./prisma/seed/lessons.ts", "./prisma/seed/index.ts", "./src/middleware.ts", "./src/utils/cache.ts", "./src/hooks/usefetch.ts", "./src/lib/prisma.ts", "./src/types/api.ts", "./node_modules/@types/bcrypt/index.d.ts", "./src/lib/db.ts", "./src/pages/api/check-api-key.ts", "./src/pages/api/set-api-key.ts", "./node_modules/openai/_shims/manual-types.d.ts", "./node_modules/openai/_shims/auto/types.d.ts", "./node_modules/openai/streaming.d.ts", "./node_modules/openai/error.d.ts", "./node_modules/openai/_shims/multipartbody.d.ts", "./node_modules/openai/uploads.d.ts", "./node_modules/openai/core.d.ts", "./node_modules/openai/_shims/index.d.ts", "./node_modules/openai/pagination.d.ts", "./node_modules/openai/resource.d.ts", "./node_modules/openai/resources/shared.d.ts", "./node_modules/openai/resources/completions.d.ts", "./node_modules/openai/resources/chat/completions/messages.d.ts", "./node_modules/openai/resources/chat/completions/completions.d.ts", "./node_modules/openai/resources/chat/chat.d.ts", "./node_modules/openai/resources/chat/completions/index.d.ts", "./node_modules/openai/resources/chat/index.d.ts", "./node_modules/openai/resources/audio/speech.d.ts", "./node_modules/openai/resources/audio/transcriptions.d.ts", "./node_modules/openai/resources/audio/translations.d.ts", "./node_modules/openai/resources/audio/audio.d.ts", "./node_modules/openai/resources/batches.d.ts", "./node_modules/openai/resources/beta/threads/messages.d.ts", "./node_modules/openai/resources/beta/threads/runs/steps.d.ts", "./node_modules/openai/resources/beta/threads/runs/runs.d.ts", "./node_modules/openai/lib/eventstream.d.ts", "./node_modules/openai/lib/assistantstream.d.ts", "./node_modules/openai/resources/beta/threads/threads.d.ts", "./node_modules/openai/resources/beta/assistants.d.ts", "./node_modules/openai/resources/chat/completions.d.ts", "./node_modules/openai/lib/abstractchatcompletionrunner.d.ts", "./node_modules/openai/lib/chatcompletionstream.d.ts", "./node_modules/openai/lib/responsesparser.d.ts", "./node_modules/openai/resources/responses/input-items.d.ts", "./node_modules/openai/lib/responses/eventtypes.d.ts", "./node_modules/openai/lib/responses/responsestream.d.ts", "./node_modules/openai/resources/responses/responses.d.ts", "./node_modules/openai/lib/parser.d.ts", "./node_modules/openai/lib/chatcompletionstreamingrunner.d.ts", "./node_modules/openai/lib/jsonschema.d.ts", "./node_modules/openai/lib/runnablefunction.d.ts", "./node_modules/openai/lib/chatcompletionrunner.d.ts", "./node_modules/openai/resources/beta/chat/completions.d.ts", "./node_modules/openai/resources/beta/chat/chat.d.ts", "./node_modules/openai/resources/beta/realtime/sessions.d.ts", "./node_modules/openai/resources/beta/realtime/transcription-sessions.d.ts", "./node_modules/openai/resources/beta/realtime/realtime.d.ts", "./node_modules/openai/resources/beta/beta.d.ts", "./node_modules/openai/resources/embeddings.d.ts", "./node_modules/openai/resources/evals/runs/output-items.d.ts", "./node_modules/openai/resources/evals/runs/runs.d.ts", "./node_modules/openai/resources/evals/evals.d.ts", "./node_modules/openai/resources/files.d.ts", "./node_modules/openai/resources/fine-tuning/checkpoints/permissions.d.ts", "./node_modules/openai/resources/fine-tuning/checkpoints/checkpoints.d.ts", "./node_modules/openai/resources/fine-tuning/jobs/checkpoints.d.ts", "./node_modules/openai/resources/fine-tuning/jobs/jobs.d.ts", "./node_modules/openai/resources/fine-tuning/fine-tuning.d.ts", "./node_modules/openai/resources/images.d.ts", "./node_modules/openai/resources/models.d.ts", "./node_modules/openai/resources/moderations.d.ts", "./node_modules/openai/resources/uploads/parts.d.ts", "./node_modules/openai/resources/uploads/uploads.d.ts", "./node_modules/openai/resources/vector-stores/files.d.ts", "./node_modules/openai/resources/vector-stores/file-batches.d.ts", "./node_modules/openai/resources/vector-stores/vector-stores.d.ts", "./node_modules/openai/resources/index.d.ts", "./node_modules/openai/index.d.ts", "./src/pages/api/tts.ts", "./src/utils/openaiclient.ts", "./node_modules/@types/ms/index.d.ts", "./node_modules/@types/jsonwebtoken/index.d.ts", "./node_modules/nookies/dist/index.d.ts", "./src/utils/authcookies.ts", "./src/utils/authservice.ts", "./src/utils/authmiddleware.ts", "./src/pages/api/ai/generate-conversation.ts", "./src/pages/api/ai/grammar-correction.ts", "./src/pages/api/ai/personalized-lesson-plan.ts", "./node_modules/@types/formidable/formidable.d.ts", "./node_modules/@types/formidable/parsers/index.d.ts", "./node_modules/@types/formidable/persistentfile.d.ts", "./node_modules/@types/formidable/volatilefile.d.ts", "./node_modules/@types/formidable/formidableerror.d.ts", "./node_modules/@types/formidable/index.d.ts", "./src/pages/api/ai/pronunciation-analysis.ts", "./src/pages/api/ai/tutor-chat.ts", "./src/pages/api/assessments/index.ts", "./src/pages/api/auth/login.ts", "./src/pages/api/auth/logout.ts", "./src/pages/api/auth/register.ts", "./node_modules/axios/index.d.ts", "./src/services/api/apiclient.ts", "./src/services/api/apiconfig.ts", "./src/services/api/conversationapiservice.ts", "./src/pages/api/conversation/[id].ts", "./src/utils/auth.ts", "./src/services/aiservice.ts", "./src/pages/api/conversation/chat.ts", "./src/pages/api/conversation/history.ts", "./src/pages/api/conversation/message.ts", "./src/pages/api/conversation/scenarios.ts", "./src/pages/api/conversation/start.ts", "./src/pages/api/conversation/topics.ts", "./src/pages/api/debug/vocabulary.ts", "./src/pages/api/exam/modules.ts", "./src/pages/api/exam/practice.ts", "./src/pages/api/exam/results.ts", "./src/pages/api/grammar/check.ts", "./src/pages/api/grammar/exercises.ts", "./src/pages/api/grammar/progress.ts", "./src/pages/api/grammar/verb-conjugation.ts", "./src/pages/api/grammar/exercises/[id].ts", "./src/pages/api/learning/assessment.ts", "./src/pages/api/learning/lessons.ts", "./src/pages/api/learning/recommended-resources.ts", "./src/pages/api/learning/vocabulary.ts", "./src/pages/api/lessons/[id].ts", "./src/pages/api/lessons/index.ts", "./src/pages/api/lessons/progress.ts", "./src/pages/api/listening/exercises.ts", "./src/pages/api/practice/session.ts", "./node_modules/openai/resources.d.ts", "./src/pages/api/practice/vocabulary.ts", "./src/services/api/pronunciationapiservice.ts", "./src/pages/api/pronunciation/check.ts", "./src/pages/api/pronunciation/exercises.ts", "./src/pages/api/pronunciation/progress.ts", "./src/pages/api/pronunciation/audio/[id].ts", "./src/pages/api/pronunciation/exercises/[id].ts", "./src/pages/api/speaking/exercises.ts", "./src/pages/api/speech/pronunciation.ts", "./src/pages/api/user/profile.ts", "./src/pages/api/user/skills.ts", "./src/pages/api/uservocabulary/index.ts", "./src/pages/api/uservocabulary/progress.ts", "./src/pages/api/vocabulary/categories.ts", "./src/pages/api/vocabulary/index.ts", "./src/pages/api/vocabulary/levels.ts", "./src/pages/api/vocabulary/progress.ts", "./src/services/conversationservice.ts", "./src/services/api/examapiservice.ts", "./src/services/examservice.ts", "./src/services/api/grammarapiservice.ts", "./src/services/grammarservice.ts", "./src/services/api/authapiservice.ts", "./src/services/api/userapiservice.ts", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/features/spacedrepetition.tsx", "./src/services/api/vocabularyapiservice.ts", "./src/services/vocabularyservice.ts", "./src/services/api/lessonapiservice.ts", "./src/services/lessonservice.ts", "./src/services/pronunciationservice.ts", "./src/services/api/listeningapiservice.ts", "./src/services/listeningservice.ts", "./src/services/api/speakingapiservice.ts", "./src/services/speakingservice.ts", "./src/services/index.ts", "./src/services/translationservice.ts", "./src/services/api/index.ts", "./src/utils/apiutils.ts", "./src/utils/prefetch.ts", "./src/context/authcontext.tsx", "./src/components/ui/loadingstate.tsx", "./src/components/auth/protectedroute.tsx", "./src/components/exam/audiorecorder.tsx", "./src/components/exam/difficultyfilter.tsx", "./src/components/exam/examquestion.tsx", "./src/components/exam/exammodule.tsx", "./src/components/exam/examprogress.tsx", "./src/components/exam/progresstracker.tsx", "./src/components/exercises/dictationexercise.tsx", "./src/components/exercises/fillinblankexercise.tsx", "./src/components/exercises/grammarexercise.tsx", "./src/components/exercises/listeningcomprehension.tsx", "./src/components/exercises/matchingexercise.tsx", "./src/components/exercises/reorderexercise.tsx", "./src/components/exercises/verbconjugation.tsx", "./src/components/features/aichat.tsx", "./src/components/features/advancedpronunciationpractice.tsx", "./src/components/features/conversationpractice.tsx", "./src/components/features/enhancedspeechrecognition.tsx", "./src/components/features/examsimulation.tsx", "./src/components/features/grammarcorrection.tsx", "./src/components/features/interactivelesson.tsx", "./src/components/features/pronunciationpractice.tsx", "./src/components/features/translator.tsx", "./src/components/features/userprofile.tsx", "./src/components/features/vocabularyquiz.tsx", "./src/components/features/voiceinput.tsx", "./src/components/ui/errormessage.tsx", "./src/components/features/writingcorrection.tsx", "./src/components/layout/header.tsx", "./src/components/layout/layout.tsx", "./src/components/progress/progresschart.tsx", "./src/components/progress/skillradarchart.tsx", "./src/components/ui/textarea.tsx", "./node_modules/lucide-react/dist/lucide-react.d.ts", "./src/components/ui/select.tsx", "./node_modules/sonner/dist/index.d.ts", "./src/components/translation/translationwidget.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/errorboundary.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/loadingscreen.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/toggle.tsx", "./node_modules/next/dist/compiled/@next/font/dist/types.d.ts", "./node_modules/next/dist/compiled/@next/font/dist/google/index.d.ts", "./node_modules/next/font/google/index.d.ts", "./src/pages/_app.tsx", "./src/pages/chat.tsx", "./src/pages/conversation.tsx", "./src/pages/dashboard.tsx", "./src/pages/exam-practice.tsx", "./src/pages/exam.tsx", "./src/pages/grammar.tsx", "./node_modules/@heroicons/react/24/outline/academiccapicon.d.ts", "./node_modules/@heroicons/react/24/outline/adjustmentshorizontalicon.d.ts", "./node_modules/@heroicons/react/24/outline/adjustmentsverticalicon.d.ts", "./node_modules/@heroicons/react/24/outline/archiveboxarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/archiveboxxmarkicon.d.ts", "./node_modules/@heroicons/react/24/outline/archiveboxicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdowncircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdownlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdownonsquarestackicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdownonsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdownrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdowntrayicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowleftcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowleftendonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowleftonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowleftstartonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowlongdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowlonglefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowlongrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowlongupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowpathroundedsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowpathicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowrightcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowrightendonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowrightonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowrightstartonrectangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsmalldownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsmalllefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsmallrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsmallupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowtoprightonsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowtrendingdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowtrendingupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturndownlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturndownrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnleftdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnleftupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnrightdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnrightupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnuplefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowturnuprighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowupcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuplefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuponsquarestackicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuponsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuprighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuptrayicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuturndownicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuturnlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuturnrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowuturnupicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowspointinginicon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowspointingouticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsrightlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/arrowsupdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/atsymbolicon.d.ts", "./node_modules/@heroicons/react/24/outline/backspaceicon.d.ts", "./node_modules/@heroicons/react/24/outline/backwardicon.d.ts", "./node_modules/@heroicons/react/24/outline/banknotesicon.d.ts", "./node_modules/@heroicons/react/24/outline/bars2icon.d.ts", "./node_modules/@heroicons/react/24/outline/bars3bottomlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/bars3bottomrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/bars3centerlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/bars3icon.d.ts", "./node_modules/@heroicons/react/24/outline/bars4icon.d.ts", "./node_modules/@heroicons/react/24/outline/barsarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/barsarrowupicon.d.ts", "./node_modules/@heroicons/react/24/outline/battery0icon.d.ts", "./node_modules/@heroicons/react/24/outline/battery100icon.d.ts", "./node_modules/@heroicons/react/24/outline/battery50icon.d.ts", "./node_modules/@heroicons/react/24/outline/beakericon.d.ts", "./node_modules/@heroicons/react/24/outline/bellalerticon.d.ts", "./node_modules/@heroicons/react/24/outline/bellslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/bellsnoozeicon.d.ts", "./node_modules/@heroicons/react/24/outline/bellicon.d.ts", "./node_modules/@heroicons/react/24/outline/boldicon.d.ts", "./node_modules/@heroicons/react/24/outline/boltslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/bolticon.d.ts", "./node_modules/@heroicons/react/24/outline/bookopenicon.d.ts", "./node_modules/@heroicons/react/24/outline/bookmarkslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/bookmarksquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/bookmarkicon.d.ts", "./node_modules/@heroicons/react/24/outline/briefcaseicon.d.ts", "./node_modules/@heroicons/react/24/outline/buganticon.d.ts", "./node_modules/@heroicons/react/24/outline/buildinglibraryicon.d.ts", "./node_modules/@heroicons/react/24/outline/buildingoffice2icon.d.ts", "./node_modules/@heroicons/react/24/outline/buildingofficeicon.d.ts", "./node_modules/@heroicons/react/24/outline/buildingstorefronticon.d.ts", "./node_modules/@heroicons/react/24/outline/cakeicon.d.ts", "./node_modules/@heroicons/react/24/outline/calculatoricon.d.ts", "./node_modules/@heroicons/react/24/outline/calendardaterangeicon.d.ts", "./node_modules/@heroicons/react/24/outline/calendardaysicon.d.ts", "./node_modules/@heroicons/react/24/outline/calendaricon.d.ts", "./node_modules/@heroicons/react/24/outline/cameraicon.d.ts", "./node_modules/@heroicons/react/24/outline/chartbarsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/chartbaricon.d.ts", "./node_modules/@heroicons/react/24/outline/chartpieicon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubblebottomcentertexticon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubblebottomcentericon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubbleleftellipsisicon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubbleleftrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubblelefticon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubbleovalleftellipsisicon.d.ts", "./node_modules/@heroicons/react/24/outline/chatbubbleovallefticon.d.ts", "./node_modules/@heroicons/react/24/outline/checkbadgeicon.d.ts", "./node_modules/@heroicons/react/24/outline/checkcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/checkicon.d.ts", "./node_modules/@heroicons/react/24/outline/chevrondoubledownicon.d.ts", "./node_modules/@heroicons/react/24/outline/chevrondoublelefticon.d.ts", "./node_modules/@heroicons/react/24/outline/chevrondoublerighticon.d.ts", "./node_modules/@heroicons/react/24/outline/chevrondoubleupicon.d.ts", "./node_modules/@heroicons/react/24/outline/chevrondownicon.d.ts", "./node_modules/@heroicons/react/24/outline/chevronlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/chevronrighticon.d.ts", "./node_modules/@heroicons/react/24/outline/chevronupdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/chevronupicon.d.ts", "./node_modules/@heroicons/react/24/outline/circlestackicon.d.ts", "./node_modules/@heroicons/react/24/outline/clipboarddocumentcheckicon.d.ts", "./node_modules/@heroicons/react/24/outline/clipboarddocumentlisticon.d.ts", "./node_modules/@heroicons/react/24/outline/clipboarddocumenticon.d.ts", "./node_modules/@heroicons/react/24/outline/clipboardicon.d.ts", "./node_modules/@heroicons/react/24/outline/clockicon.d.ts", "./node_modules/@heroicons/react/24/outline/cloudarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/cloudarrowupicon.d.ts", "./node_modules/@heroicons/react/24/outline/cloudicon.d.ts", "./node_modules/@heroicons/react/24/outline/codebracketsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/codebracketicon.d.ts", "./node_modules/@heroicons/react/24/outline/cog6toothicon.d.ts", "./node_modules/@heroicons/react/24/outline/cog8toothicon.d.ts", "./node_modules/@heroicons/react/24/outline/cogicon.d.ts", "./node_modules/@heroicons/react/24/outline/commandlineicon.d.ts", "./node_modules/@heroicons/react/24/outline/computerdesktopicon.d.ts", "./node_modules/@heroicons/react/24/outline/cpuchipicon.d.ts", "./node_modules/@heroicons/react/24/outline/creditcardicon.d.ts", "./node_modules/@heroicons/react/24/outline/cubetransparenticon.d.ts", "./node_modules/@heroicons/react/24/outline/cubeicon.d.ts", "./node_modules/@heroicons/react/24/outline/currencybangladeshiicon.d.ts", "./node_modules/@heroicons/react/24/outline/currencydollaricon.d.ts", "./node_modules/@heroicons/react/24/outline/currencyeuroicon.d.ts", "./node_modules/@heroicons/react/24/outline/currencypoundicon.d.ts", "./node_modules/@heroicons/react/24/outline/currencyrupeeicon.d.ts", "./node_modules/@heroicons/react/24/outline/currencyyenicon.d.ts", "./node_modules/@heroicons/react/24/outline/cursorarrowraysicon.d.ts", "./node_modules/@heroicons/react/24/outline/cursorarrowrippleicon.d.ts", "./node_modules/@heroicons/react/24/outline/devicephonemobileicon.d.ts", "./node_modules/@heroicons/react/24/outline/devicetableticon.d.ts", "./node_modules/@heroicons/react/24/outline/divideicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentarrowupicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentchartbaricon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcheckicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencybangladeshiicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencydollaricon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencyeuroicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencypoundicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencyrupeeicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentcurrencyyenicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentduplicateicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentmagnifyingglassicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentminusicon.d.ts", "./node_modules/@heroicons/react/24/outline/documentplusicon.d.ts", "./node_modules/@heroicons/react/24/outline/documenttexticon.d.ts", "./node_modules/@heroicons/react/24/outline/documenticon.d.ts", "./node_modules/@heroicons/react/24/outline/ellipsishorizontalcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/ellipsishorizontalicon.d.ts", "./node_modules/@heroicons/react/24/outline/ellipsisverticalicon.d.ts", "./node_modules/@heroicons/react/24/outline/envelopeopenicon.d.ts", "./node_modules/@heroicons/react/24/outline/envelopeicon.d.ts", "./node_modules/@heroicons/react/24/outline/equalsicon.d.ts", "./node_modules/@heroicons/react/24/outline/exclamationcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/exclamationtriangleicon.d.ts", "./node_modules/@heroicons/react/24/outline/eyedroppericon.d.ts", "./node_modules/@heroicons/react/24/outline/eyeslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/eyeicon.d.ts", "./node_modules/@heroicons/react/24/outline/facefrownicon.d.ts", "./node_modules/@heroicons/react/24/outline/facesmileicon.d.ts", "./node_modules/@heroicons/react/24/outline/filmicon.d.ts", "./node_modules/@heroicons/react/24/outline/fingerprinticon.d.ts", "./node_modules/@heroicons/react/24/outline/fireicon.d.ts", "./node_modules/@heroicons/react/24/outline/flagicon.d.ts", "./node_modules/@heroicons/react/24/outline/folderarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/folderminusicon.d.ts", "./node_modules/@heroicons/react/24/outline/folderopenicon.d.ts", "./node_modules/@heroicons/react/24/outline/folderplusicon.d.ts", "./node_modules/@heroicons/react/24/outline/foldericon.d.ts", "./node_modules/@heroicons/react/24/outline/forwardicon.d.ts", "./node_modules/@heroicons/react/24/outline/funnelicon.d.ts", "./node_modules/@heroicons/react/24/outline/gificon.d.ts", "./node_modules/@heroicons/react/24/outline/gifttopicon.d.ts", "./node_modules/@heroicons/react/24/outline/gifticon.d.ts", "./node_modules/@heroicons/react/24/outline/globealticon.d.ts", "./node_modules/@heroicons/react/24/outline/globeamericasicon.d.ts", "./node_modules/@heroicons/react/24/outline/globeasiaaustraliaicon.d.ts", "./node_modules/@heroicons/react/24/outline/globeeuropeafricaicon.d.ts", "./node_modules/@heroicons/react/24/outline/h1icon.d.ts", "./node_modules/@heroicons/react/24/outline/h2icon.d.ts", "./node_modules/@heroicons/react/24/outline/h3icon.d.ts", "./node_modules/@heroicons/react/24/outline/handraisedicon.d.ts", "./node_modules/@heroicons/react/24/outline/handthumbdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/handthumbupicon.d.ts", "./node_modules/@heroicons/react/24/outline/hashtagicon.d.ts", "./node_modules/@heroicons/react/24/outline/hearticon.d.ts", "./node_modules/@heroicons/react/24/outline/homemodernicon.d.ts", "./node_modules/@heroicons/react/24/outline/homeicon.d.ts", "./node_modules/@heroicons/react/24/outline/identificationicon.d.ts", "./node_modules/@heroicons/react/24/outline/inboxarrowdownicon.d.ts", "./node_modules/@heroicons/react/24/outline/inboxstackicon.d.ts", "./node_modules/@heroicons/react/24/outline/inboxicon.d.ts", "./node_modules/@heroicons/react/24/outline/informationcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/italicicon.d.ts", "./node_modules/@heroicons/react/24/outline/keyicon.d.ts", "./node_modules/@heroicons/react/24/outline/languageicon.d.ts", "./node_modules/@heroicons/react/24/outline/lifebuoyicon.d.ts", "./node_modules/@heroicons/react/24/outline/lightbulbicon.d.ts", "./node_modules/@heroicons/react/24/outline/linkslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/linkicon.d.ts", "./node_modules/@heroicons/react/24/outline/listbulleticon.d.ts", "./node_modules/@heroicons/react/24/outline/lockclosedicon.d.ts", "./node_modules/@heroicons/react/24/outline/lockopenicon.d.ts", "./node_modules/@heroicons/react/24/outline/magnifyingglasscircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/magnifyingglassminusicon.d.ts", "./node_modules/@heroicons/react/24/outline/magnifyingglassplusicon.d.ts", "./node_modules/@heroicons/react/24/outline/magnifyingglassicon.d.ts", "./node_modules/@heroicons/react/24/outline/mappinicon.d.ts", "./node_modules/@heroicons/react/24/outline/mapicon.d.ts", "./node_modules/@heroicons/react/24/outline/megaphoneicon.d.ts", "./node_modules/@heroicons/react/24/outline/microphoneicon.d.ts", "./node_modules/@heroicons/react/24/outline/minuscircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/minussmallicon.d.ts", "./node_modules/@heroicons/react/24/outline/minusicon.d.ts", "./node_modules/@heroicons/react/24/outline/moonicon.d.ts", "./node_modules/@heroicons/react/24/outline/musicalnoteicon.d.ts", "./node_modules/@heroicons/react/24/outline/newspapericon.d.ts", "./node_modules/@heroicons/react/24/outline/nosymbolicon.d.ts", "./node_modules/@heroicons/react/24/outline/numberedlisticon.d.ts", "./node_modules/@heroicons/react/24/outline/paintbrushicon.d.ts", "./node_modules/@heroicons/react/24/outline/paperairplaneicon.d.ts", "./node_modules/@heroicons/react/24/outline/paperclipicon.d.ts", "./node_modules/@heroicons/react/24/outline/pausecircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/pauseicon.d.ts", "./node_modules/@heroicons/react/24/outline/pencilsquareicon.d.ts", "./node_modules/@heroicons/react/24/outline/pencilicon.d.ts", "./node_modules/@heroicons/react/24/outline/percentbadgeicon.d.ts", "./node_modules/@heroicons/react/24/outline/phonearrowdownlefticon.d.ts", "./node_modules/@heroicons/react/24/outline/phonearrowuprighticon.d.ts", "./node_modules/@heroicons/react/24/outline/phonexmarkicon.d.ts", "./node_modules/@heroicons/react/24/outline/phoneicon.d.ts", "./node_modules/@heroicons/react/24/outline/photoicon.d.ts", "./node_modules/@heroicons/react/24/outline/playcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/playpauseicon.d.ts", "./node_modules/@heroicons/react/24/outline/playicon.d.ts", "./node_modules/@heroicons/react/24/outline/pluscircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/plussmallicon.d.ts", "./node_modules/@heroicons/react/24/outline/plusicon.d.ts", "./node_modules/@heroicons/react/24/outline/powericon.d.ts", "./node_modules/@heroicons/react/24/outline/presentationchartbaricon.d.ts", "./node_modules/@heroicons/react/24/outline/presentationchartlineicon.d.ts", "./node_modules/@heroicons/react/24/outline/printericon.d.ts", "./node_modules/@heroicons/react/24/outline/puzzlepieceicon.d.ts", "./node_modules/@heroicons/react/24/outline/qrcodeicon.d.ts", "./node_modules/@heroicons/react/24/outline/questionmarkcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/queuelisticon.d.ts", "./node_modules/@heroicons/react/24/outline/radioicon.d.ts", "./node_modules/@heroicons/react/24/outline/receiptpercenticon.d.ts", "./node_modules/@heroicons/react/24/outline/receiptrefundicon.d.ts", "./node_modules/@heroicons/react/24/outline/rectanglegroupicon.d.ts", "./node_modules/@heroicons/react/24/outline/rectanglestackicon.d.ts", "./node_modules/@heroicons/react/24/outline/rocketlaunchicon.d.ts", "./node_modules/@heroicons/react/24/outline/rssicon.d.ts", "./node_modules/@heroicons/react/24/outline/scaleicon.d.ts", "./node_modules/@heroicons/react/24/outline/scissorsicon.d.ts", "./node_modules/@heroicons/react/24/outline/serverstackicon.d.ts", "./node_modules/@heroicons/react/24/outline/servericon.d.ts", "./node_modules/@heroicons/react/24/outline/shareicon.d.ts", "./node_modules/@heroicons/react/24/outline/shieldcheckicon.d.ts", "./node_modules/@heroicons/react/24/outline/shieldexclamationicon.d.ts", "./node_modules/@heroicons/react/24/outline/shoppingbagicon.d.ts", "./node_modules/@heroicons/react/24/outline/shoppingcarticon.d.ts", "./node_modules/@heroicons/react/24/outline/signalslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/signalicon.d.ts", "./node_modules/@heroicons/react/24/outline/slashicon.d.ts", "./node_modules/@heroicons/react/24/outline/sparklesicon.d.ts", "./node_modules/@heroicons/react/24/outline/speakerwaveicon.d.ts", "./node_modules/@heroicons/react/24/outline/speakerxmarkicon.d.ts", "./node_modules/@heroicons/react/24/outline/square2stackicon.d.ts", "./node_modules/@heroicons/react/24/outline/square3stack3dicon.d.ts", "./node_modules/@heroicons/react/24/outline/squares2x2icon.d.ts", "./node_modules/@heroicons/react/24/outline/squaresplusicon.d.ts", "./node_modules/@heroicons/react/24/outline/staricon.d.ts", "./node_modules/@heroicons/react/24/outline/stopcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/stopicon.d.ts", "./node_modules/@heroicons/react/24/outline/strikethroughicon.d.ts", "./node_modules/@heroicons/react/24/outline/sunicon.d.ts", "./node_modules/@heroicons/react/24/outline/swatchicon.d.ts", "./node_modules/@heroicons/react/24/outline/tablecellsicon.d.ts", "./node_modules/@heroicons/react/24/outline/tagicon.d.ts", "./node_modules/@heroicons/react/24/outline/ticketicon.d.ts", "./node_modules/@heroicons/react/24/outline/trashicon.d.ts", "./node_modules/@heroicons/react/24/outline/trophyicon.d.ts", "./node_modules/@heroicons/react/24/outline/truckicon.d.ts", "./node_modules/@heroicons/react/24/outline/tvicon.d.ts", "./node_modules/@heroicons/react/24/outline/underlineicon.d.ts", "./node_modules/@heroicons/react/24/outline/usercircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/usergroupicon.d.ts", "./node_modules/@heroicons/react/24/outline/userminusicon.d.ts", "./node_modules/@heroicons/react/24/outline/userplusicon.d.ts", "./node_modules/@heroicons/react/24/outline/usericon.d.ts", "./node_modules/@heroicons/react/24/outline/usersicon.d.ts", "./node_modules/@heroicons/react/24/outline/variableicon.d.ts", "./node_modules/@heroicons/react/24/outline/videocameraslashicon.d.ts", "./node_modules/@heroicons/react/24/outline/videocameraicon.d.ts", "./node_modules/@heroicons/react/24/outline/viewcolumnsicon.d.ts", "./node_modules/@heroicons/react/24/outline/viewfindercircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/walleticon.d.ts", "./node_modules/@heroicons/react/24/outline/wifiicon.d.ts", "./node_modules/@heroicons/react/24/outline/windowicon.d.ts", "./node_modules/@heroicons/react/24/outline/wrenchscrewdrivericon.d.ts", "./node_modules/@heroicons/react/24/outline/wrenchicon.d.ts", "./node_modules/@heroicons/react/24/outline/xcircleicon.d.ts", "./node_modules/@heroicons/react/24/outline/xmarkicon.d.ts", "./node_modules/@heroicons/react/24/outline/index.d.ts", "./src/pages/index.tsx", "./src/pages/listening.tsx", "./src/pages/login.tsx", "./src/pages/practice.tsx", "./src/pages/proficiency-test.tsx", "./src/pages/profile.tsx", "./src/pages/progress.tsx", "./src/pages/pronunciation.tsx", "./src/pages/register.tsx", "./src/pages/settings.tsx", "./src/pages/verb-conjugation.tsx", "./src/pages/vocabulary.tsx", "./src/pages/writing.tsx", "./src/pages/exam-practice/[moduleid].tsx", "./src/pages/exam-practice/progress.tsx", "./src/pages/lessons/[id].tsx", "./src/pages/lessons/index.tsx", "./src/pages/tools/translator.tsx", "./node_modules/@types/json5/index.d.ts", "./node_modules/form-data/index.d.ts", "./node_modules/@types/node-fetch/externals.d.ts", "./node_modules/@types/node-fetch/index.d.ts", "./node_modules/@types/use-sync-external-store/index.d.ts"], "fileIdsList": [[97, 140, 404, 405], [97, 140, 407], [85, 97, 140], [97, 140, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 827, 828, 829, 830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 853, 854, 855, 856, 857, 858, 859, 860, 861, 862, 863, 864, 865, 866, 867, 868, 869, 870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 880, 881, 882, 883, 884, 885, 886, 887, 888, 889, 890, 891, 892, 893, 894, 895, 896, 897, 898, 899, 900, 901, 902, 903, 904, 905, 906, 907, 908, 909, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920, 921, 922, 923, 924, 925, 926, 927, 928, 929, 930, 931, 932, 933, 934, 935, 936, 937, 938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952, 953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965, 966, 967, 968, 969], [97, 140], [97, 140, 408], [97, 140, 189], [97, 140, 155, 171, 509], [97, 140, 171, 189, 504, 505, 506, 507, 508], [97, 140, 171, 509], [97, 140, 152, 509], [97, 140, 145, 189, 495], [97, 140, 155, 182, 189, 990, 991], [97, 137, 140], [97, 139, 140], [140], [97, 140, 145, 174], [97, 140, 141, 146, 152, 153, 160, 171, 182], [97, 140, 141, 142, 152, 160], [92, 93, 94, 97, 140], [97, 140, 143, 183], [97, 140, 144, 145, 153, 161], [97, 140, 145, 171, 179], [97, 140, 146, 148, 152, 160], [97, 139, 140, 147], [97, 140, 148, 149], [97, 140, 152], [97, 140, 150, 152], [97, 139, 140, 152], [97, 140, 152, 153, 154, 171, 182], [97, 140, 152, 153, 154, 167, 171, 174], [97, 135, 140, 187], [97, 140, 148, 152, 155, 160, 171, 182], [97, 140, 152, 153, 155, 156, 160, 171, 179, 182], [97, 140, 155, 157, 171, 179, 182], [95, 96, 97, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188], [97, 140, 152, 158], [97, 140, 159, 182, 187], [97, 140, 148, 152, 160, 171], [97, 140, 161], [97, 140, 162], [97, 139, 140, 163], [97, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188], [97, 140, 165], [97, 140, 166], [97, 140, 152, 167, 168], [97, 140, 167, 169, 183, 185], [97, 140, 152, 171, 172, 174], [97, 140, 173, 174], [97, 140, 171, 172], [97, 140, 174], [97, 140, 175], [97, 137, 140, 171], [97, 140, 152, 177, 178], [97, 140, 177, 178], [97, 140, 145, 160, 171, 179], [97, 140, 180], [97, 140, 160, 181], [97, 140, 155, 166, 182], [97, 140, 145, 183], [97, 140, 171, 184], [97, 140, 159, 185], [97, 140, 186], [97, 140, 145, 152, 154, 163, 171, 182, 185, 187], [97, 140, 171, 188], [85, 97, 140, 193, 194, 195], [85, 97, 140, 193, 194], [85, 89, 97, 140, 192, 357, 400], [85, 89, 97, 140, 191, 357, 400], [82, 83, 84, 97, 140], [97, 140, 155, 171, 189], [90, 97, 140], [97, 140, 361], [97, 140, 363, 364, 365], [97, 140, 367], [97, 140, 198, 208, 214, 216, 357], [97, 140, 198, 205, 207, 210, 228], [97, 140, 208], [97, 140, 208, 210, 335], [97, 140, 263, 281, 296, 403], [97, 140, 305], [97, 140, 198, 208, 215, 249, 259, 332, 333, 403], [97, 140, 215, 403], [97, 140, 208, 259, 260, 261, 403], [97, 140, 208, 215, 249, 403], [97, 140, 403], [97, 140, 198, 215, 216, 403], [97, 140, 289], [97, 139, 140, 189, 288], [85, 97, 140, 282, 283, 284, 302, 303], [85, 97, 140, 282], [97, 140, 272], [97, 140, 271, 273, 377], [85, 97, 140, 282, 283, 300], [97, 140, 278, 303, 389], [97, 140, 387, 388], [97, 140, 222, 386], [97, 140, 275], [97, 139, 140, 189, 222, 238, 271, 272, 273, 274], [85, 97, 140, 300, 302, 303], [97, 140, 300, 302], [97, 140, 300, 301, 303], [97, 140, 166, 189], [97, 140, 270], [97, 139, 140, 189, 207, 209, 266, 267, 268, 269], [85, 97, 140, 199, 380], [85, 97, 140, 182, 189], [85, 97, 140, 215, 247], [85, 97, 140, 215], [97, 140, 245, 250], [85, 97, 140, 246, 360], [97, 140, 636], [85, 89, 97, 140, 155, 189, 191, 192, 357, 398, 399], [97, 140, 357], [97, 140, 197], [97, 140, 350, 351, 352, 353, 354, 355], [97, 140, 352], [85, 97, 140, 246, 282, 360], [85, 97, 140, 282, 358, 360], [85, 97, 140, 282, 360], [97, 140, 155, 189, 209, 360], [97, 140, 155, 189, 206, 207, 218, 236, 238, 270, 275, 276, 298, 300], [97, 140, 267, 270, 275, 283, 285, 286, 287, 289, 290, 291, 292, 293, 294, 295, 403], [97, 140, 268], [85, 97, 140, 166, 189, 207, 208, 236, 238, 239, 241, 266, 298, 299, 303, 357, 403], [97, 140, 155, 189, 209, 210, 222, 223, 271], [97, 140, 155, 189, 208, 210], [97, 140, 155, 171, 189, 206, 209, 210], [97, 140, 155, 166, 182, 189, 206, 207, 208, 209, 210, 215, 218, 219, 229, 230, 232, 235, 236, 238, 239, 240, 241, 265, 266, 299, 300, 308, 310, 313, 315, 318, 320, 321, 322, 323], [97, 140, 198, 199, 200, 206, 207, 357, 360, 403], [97, 140, 155, 171, 182, 189, 203, 334, 336, 337, 403], [97, 140, 166, 182, 189, 203, 206, 209, 226, 230, 232, 233, 234, 239, 266, 313, 324, 326, 332, 346, 347], [97, 140, 208, 212, 266], [97, 140, 206, 208], [97, 140, 219, 314], [97, 140, 316, 317], [97, 140, 316], [97, 140, 314], [97, 140, 316, 319], [97, 140, 202, 203], [97, 140, 202, 242], [97, 140, 202], [97, 140, 204, 219, 312], [97, 140, 311], [97, 140, 203, 204], [97, 140, 204, 309], [97, 140, 203], [97, 140, 298], [97, 140, 155, 189, 206, 218, 237, 257, 263, 277, 280, 297, 300], [97, 140, 251, 252, 253, 254, 255, 256, 278, 279, 303, 358], [97, 140, 307], [97, 140, 155, 189, 206, 218, 237, 243, 304, 306, 308, 357, 360], [97, 140, 155, 182, 189, 199, 206, 208, 265], [97, 140, 262], [97, 140, 155, 189, 340, 345], [97, 140, 229, 238, 265, 360], [97, 140, 328, 332, 346, 349], [97, 140, 155, 212, 332, 340, 341, 349], [97, 140, 198, 208, 229, 240, 343], [97, 140, 155, 189, 208, 215, 240, 327, 328, 338, 339, 342, 344], [97, 140, 190, 236, 237, 238, 357, 360], [97, 140, 155, 166, 182, 189, 204, 206, 207, 209, 212, 217, 218, 226, 229, 230, 232, 233, 234, 235, 239, 241, 265, 266, 310, 324, 325, 360], [97, 140, 155, 189, 206, 208, 212, 326, 348], [97, 140, 155, 189, 207, 209], [85, 97, 140, 155, 166, 189, 197, 199, 206, 207, 210, 218, 235, 236, 238, 239, 241, 307, 357, 360], [97, 140, 155, 166, 182, 189, 201, 204, 205, 209], [97, 140, 202, 264], [97, 140, 155, 189, 202, 207, 218], [97, 140, 155, 189, 208, 219], [97, 140, 155, 189], [97, 140, 222], [97, 140, 221], [97, 140, 223], [97, 140, 208, 220, 222, 226], [97, 140, 208, 220, 222], [97, 140, 155, 189, 201, 208, 209, 215, 223, 224, 225], [85, 97, 140, 300, 301, 302], [97, 140, 258], [85, 97, 140, 199], [85, 97, 140, 232], [85, 97, 140, 190, 235, 238, 241, 357, 360], [97, 140, 199, 380, 381], [85, 97, 140, 250], [85, 97, 140, 166, 182, 189, 197, 244, 246, 248, 249, 360], [97, 140, 209, 215, 232], [97, 140, 231], [85, 97, 140, 153, 155, 166, 189, 197, 250, 259, 357, 358, 359], [81, 85, 86, 87, 88, 97, 140, 191, 192, 357, 400], [97, 140, 145], [97, 140, 329, 330, 331], [97, 140, 329], [97, 140, 369], [97, 140, 371], [97, 140, 373], [97, 140, 637], [97, 140, 375], [97, 140, 378], [97, 140, 382], [89, 91, 97, 140, 357, 362, 366, 368, 370, 372, 374, 376, 379, 383, 385, 391, 392, 394, 401, 402, 403], [97, 140, 384], [97, 140, 390], [97, 140, 246], [97, 140, 393], [97, 139, 140, 223, 224, 225, 226, 395, 396, 397, 400], [85, 89, 97, 140, 155, 157, 166, 189, 191, 192, 193, 195, 197, 210, 349, 356, 360, 400], [97, 140, 404], [97, 140, 425, 426, 431], [97, 140, 427, 428, 430, 432], [97, 140, 431], [97, 140, 428, 430, 431, 432, 433, 436, 438, 439, 445, 446, 461, 472, 473, 476, 477, 482, 483, 484, 485, 487, 490, 491], [97, 140, 431, 436, 450, 454, 463, 465, 466, 467, 492], [97, 140, 431, 432, 447, 448, 449, 450, 452, 453], [97, 140, 454, 455, 462, 465, 492], [97, 140, 431, 432, 438, 455, 467, 492], [97, 140, 432, 454, 455, 456, 462, 465, 492], [97, 140, 428], [97, 140, 435, 454, 461, 467], [97, 140, 461], [97, 140, 431, 450, 459, 461, 492], [97, 140, 454, 461, 462], [97, 140, 463, 464, 466], [97, 140, 492], [97, 140, 491], [97, 140, 434, 442, 443, 444], [97, 140, 431, 432, 434], [97, 140, 427, 431, 434, 443, 445], [97, 140, 431, 434, 443, 445], [97, 140, 431, 433, 434, 435, 446], [97, 140, 431, 433, 434, 435, 447, 448, 449, 451, 452], [97, 140, 434, 452, 453, 468, 471], [97, 140, 434, 467], [97, 140, 431, 434, 454, 455, 456, 462, 463, 465, 466], [97, 140, 434, 435, 469, 470, 471], [97, 140, 431, 434], [97, 140, 431, 433, 434, 435, 453], [97, 140, 427, 431, 433, 434, 435, 447, 448, 449, 451, 452, 453], [97, 140, 431, 433, 434, 435, 448], [97, 140, 427, 431, 434, 435, 447, 449, 451, 452, 453], [97, 140, 434, 435, 438], [97, 140, 438], [97, 140, 427, 431, 433, 434, 435, 436, 437, 438], [97, 140, 437, 438], [97, 140, 431, 433, 434, 438], [97, 140, 439, 440], [97, 140, 427, 431, 434, 436, 438], [97, 140, 431, 433, 434, 435, 461, 475], [97, 140, 431, 433, 434, 475], [97, 140, 431, 433, 434, 435, 461, 474], [97, 140, 431, 432, 433, 434], [97, 140, 434, 478], [97, 140, 431, 433, 434], [97, 140, 434, 479, 481], [97, 140, 431, 433, 434, 480], [97, 140, 435, 436, 441, 445, 446, 461, 472, 473, 476, 477, 482, 483, 484, 485, 487, 490], [97, 140, 431, 433, 434, 461], [97, 140, 427, 431, 433, 434, 435, 457, 458, 460, 461], [97, 140, 431, 434, 477, 486], [97, 140, 431, 433, 434, 488, 490], [97, 140, 431, 433, 434, 490], [97, 140, 431, 433, 434, 435, 488, 489], [97, 140, 432], [97, 140, 429, 431, 432], [97, 107, 111, 140, 182], [97, 107, 140, 171, 182], [97, 102, 140], [97, 104, 107, 140, 179, 182], [97, 140, 160, 179], [97, 102, 140, 189], [97, 104, 107, 140, 160, 182], [97, 99, 100, 103, 106, 140, 152, 171, 182], [97, 107, 114, 140], [97, 99, 105, 140], [97, 107, 128, 129, 140], [97, 103, 107, 140, 174, 182, 189], [97, 128, 140, 189], [97, 101, 102, 140, 189], [97, 107, 140], [97, 101, 102, 103, 104, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 129, 130, 131, 132, 133, 134, 140], [97, 107, 122, 140], [97, 107, 114, 115, 140], [97, 105, 107, 115, 116, 140], [97, 106, 140], [97, 99, 102, 107, 140], [97, 107, 111, 115, 116, 140], [97, 111, 140], [97, 105, 107, 110, 140, 182], [97, 99, 104, 107, 114, 140], [97, 140, 171], [97, 102, 107, 128, 140, 187, 189], [97, 140, 153, 162, 409], [97, 140, 153, 162, 409, 410, 411, 412, 413, 414], [85, 97, 140, 392, 589, 590], [85, 97, 140, 572], [85, 97, 140, 392, 572, 594], [85, 97, 140, 572, 595], [85, 97, 140, 572, 573], [85, 97, 140, 572, 573, 579, 584], [85, 97, 140, 572, 590], [85, 97, 140, 572, 579, 590], [85, 97, 140, 572, 585], [85, 97, 140, 572, 574], [85, 97, 140, 572, 573, 584, 590, 617], [85, 97, 140, 385, 392, 589], [85, 97, 140, 619], [85, 97, 140, 572, 573, 585, 623, 624, 625, 626], [85, 97, 140, 617], [85, 97, 140, 624], [85, 97, 140, 420, 499, 584], [85, 97, 140, 417], [97, 140, 419, 420, 421], [97, 140, 409], [97, 140, 401], [85, 97, 140, 362, 392, 588, 589, 620, 626, 638], [97, 140, 404, 494, 500], [97, 140, 404, 419, 494, 500], [97, 140, 153, 161, 162, 404, 494, 500, 509], [97, 140, 404, 419, 500], [97, 140, 404, 419, 420, 421, 422, 496], [97, 140, 404, 420], [97, 140, 404, 420, 422, 496], [97, 140, 404, 420, 519], [97, 140, 404, 419, 420, 521, 522], [97, 140, 404, 420, 509, 519], [97, 140, 404, 419, 420, 521], [97, 140, 404, 419], [97, 140, 404, 521], [97, 140, 404, 420, 521], [97, 140, 404, 419, 521], [97, 140, 404, 419, 494, 500, 547], [97, 140, 153, 162, 404], [97, 140, 404, 420, 509, 549], [97, 140, 404, 420, 549], [97, 140, 404, 492], [97, 140, 404, 420, 422, 521], [97, 140, 404, 419, 500, 521], [97, 140, 404, 419, 420, 500], [85, 97, 140, 376, 572, 573, 589, 605], [85, 97, 140, 376, 572, 573, 589, 607], [85, 97, 140, 376, 589, 591], [85, 97, 140, 376, 385, 572, 573, 592, 593, 597], [85, 97, 140, 376, 392, 572, 584, 590, 594, 595, 617], [85, 97, 140, 376, 385, 392, 567, 572, 590, 595, 596, 617], [85, 97, 140, 376, 572, 573, 589, 609], [85, 97, 140, 376, 572, 573, 589, 610], [85, 97, 140, 376, 383, 385, 392, 572, 573, 589, 970], [85, 97, 140, 376, 385, 392, 572, 573, 589, 611], [85, 97, 140, 376, 385, 418, 572, 573, 584, 589, 590, 617], [85, 97, 140, 376, 572, 573, 584, 589, 590, 598, 601, 617], [85, 97, 140, 376, 385, 392, 404, 572, 589, 626], [85, 97, 140, 376, 572, 573, 616], [85, 97, 140, 376, 385, 572], [85, 97, 140, 376, 385, 420, 517, 572, 573, 589, 590, 614], [85, 97, 140, 376, 572, 573, 621, 622], [85, 97, 140, 376, 385, 517, 518, 549, 572, 573, 589, 612], [85, 97, 140, 376, 499, 572, 573], [85, 97, 140, 376, 392, 613, 620], [85, 97, 140, 376, 572, 573, 589, 604], [85, 97, 140, 376, 385, 418, 572, 574, 576, 589, 590, 615, 617], [97, 140, 376, 573, 618], [97, 140, 417, 499, 516], [97, 140, 516], [97, 140, 517, 518], [97, 140, 420, 517, 518], [97, 140, 420, 517], [97, 140, 517, 518, 519, 549, 566, 568, 570, 571, 575, 577, 580, 582], [97, 140, 420, 519], [97, 140, 566], [97, 140, 568], [97, 140, 519, 522, 549, 565, 566, 567, 568, 569, 570, 571, 575, 576, 577, 578, 579, 580, 581, 582, 583], [97, 140, 420, 577], [97, 140, 417, 580], [97, 140, 549], [97, 140, 417, 522, 582], [97, 140, 574, 575], [97, 140, 404, 422, 496], [97, 140, 497], [97, 140, 404, 496, 499], [97, 140, 420, 498], [97, 140, 153, 492], [97, 140, 417, 584]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "impliedFormat": 1}, {"version": "ee7bad0c15b58988daa84371e0b89d313b762ab83cb5b31b8a2d1162e8eb41c2", "impliedFormat": 1}, {"version": "27bdc30a0e32783366a5abeda841bc22757c1797de8681bbe81fbc735eeb1c10", "impliedFormat": 1}, {"version": "8fd575e12870e9944c7e1d62e1f5a73fcf23dd8d3a321f2a2c74c20d022283fe", "impliedFormat": 1}, {"version": "8bf8b5e44e3c9c36f98e1007e8b7018c0f38d8adc07aecef42f5200114547c70", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "af3dd424cf267428f30ccfc376f47a2c0114546b55c44d8c0f1d57d841e28d74", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "959d36cddf5e7d572a65045b876f2956c973a586da58e5d26cde519184fd9b8a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "965f36eae237dd74e6cca203a43e9ca801ce38824ead814728a2807b1910117d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3925a6c820dcb1a06506c90b1577db1fdbf7705d65b62b99dce4be75c637e26b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a3d63ef2b853447ec4f749d3f368ce642264246e02911fcb1590d8c161b8005", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b5ce7a470bc3628408429040c4e3a53a27755022a32fd05e2cb694e7015386c7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b4b67b1a91182421f5df999988c690f14d813b9850b40acd06ed44691f6727ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "df83c2a6c73228b625b0beb6669c7ee2a09c914637e2d35170723ad49c0f5cd4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "436aaf437562f276ec2ddbee2f2cdedac7664c1e4c1d2c36839ddd582eeb3d0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e3c06ea092138bf9fa5e874a1fdbc9d54805d074bee1de31b99a11e2fec239d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "87dc0f382502f5bbce5129bdc0aea21e19a3abbc19259e0b43ae038a9fc4e326", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b1cb28af0c891c8c96b2d6b7be76bd394fddcfdb4709a20ba05a7c1605eea0f9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2fef54945a13095fdb9b84f705f2b5994597640c46afeb2ce78352fab4cb3279", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac77cb3e8c6d3565793eb90a8373ee8033146315a3dbead3bde8db5eaf5e5ec6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "56e4ed5aab5f5920980066a9409bfaf53e6d21d3f8d020c17e4de584d29600ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ece9f17b3866cc077099c73f4983bddbcb1dc7ddb943227f1ec070f529dedd1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a6282c8827e4b9a95f4bf4f5c205673ada31b982f50572d27103df8ceb8013c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1c9319a09485199c1f7b0498f2988d6d2249793ef67edda49d1e584746be9032", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e3a2a0cee0f03ffdde24d89660eba2685bfbdeae955a6c67e8c4c9fd28928eeb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811c71eee4aa0ac5f7adf713323a5c41b0cf6c4e17367a34fbce379e12bbf0a4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "51ad4c928303041605b4d7ae32e0c1ee387d43a24cd6f1ebf4a2699e1076d4fa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "60037901da1a425516449b9a20073aa03386cce92f7a1fd902d7602be3a7c2e9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d4b1d2c51d058fc21ec2629fff7a76249dec2e36e12960ea056e3ef89174080f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "22adec94ef7047a6c9d1af3cb96be87a335908bf9ef386ae9fd50eeb37f44c47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4245fee526a7d1754529d19227ecbf3be066ff79ebb6a380d78e41648f2f224d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0990a7576222f248f0a3b888adcb7389f957928ce2afb1cd5128169086ff4d29", "impliedFormat": 1}, {"version": "36a2e4c9a67439aca5f91bb304611d5ae6e20d420503e96c230cf8fcdc948d94", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "65ff5a0aefd7817a03c1ad04fee85c9cdd3ec415cc3c9efec85d8008d4d5e4ee", "impliedFormat": 1}, {"version": "b2546f0fbeae6ef5e232c04100e1d8c49d36d1fff8e4755f663a3e3f06e7f2d6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "cc69795d9954ee4ad57545b10c7bf1a7260d990231b1685c147ea71a6faa265c", "impliedFormat": 1}, {"version": "8bc6c94ff4f2af1f4023b7bb2379b08d3d7dd80c698c9f0b07431ea16101f05f", "impliedFormat": 1}, {"version": "1b61d259de5350f8b1e5db06290d31eaebebc6baafd5f79d314b5af9256d7153", "impliedFormat": 1}, {"version": "57194e1f007f3f2cbef26fa299d4c6b21f4623a2eddc63dfeef79e38e187a36e", "impliedFormat": 1}, {"version": "0f6666b58e9276ac3a38fdc80993d19208442d6027ab885580d93aec76b4ef00", "impliedFormat": 1}, {"version": "8edb42c9d82147b7b12cbde8f5853ed06a33ce3ebbf39de51825905ace7e01cc", "impliedFormat": 1}, {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6b80c6175da9de59bace50a72c2d68490d4ab5b07016ff5367bc7ba33cf2f219", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4967529644e391115ca5592184d4b63980569adf60ee685f968fd59ab1557188", "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "24bd580b5743dc56402c440dc7f9a4f5d592ad7a419f25414d37a7bfe11e342b", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "929b4c540b70efdaee3390e40d123889e3410c022d49edeb81b36acd54f28b4e", "impliedFormat": 1}, {"version": "6401927b62c7d8e62eb9af3b2ac6f5597e31f185b2052a0e891f8b0b1f04eef9", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "f8edab1fd539c4bc67932e1d0a37588528febc37ad8ee555d8a28aae30a7b770", "impliedFormat": 1}, {"version": "62dbb9afbfbe0f14c68963c21034da7cd82fc752c8191f224dbbafaf470347e1", "impliedFormat": 1}, {"version": "19fc8acc7a858de2d5f66d4bc31d74321c537cff4af293e7f8bf7554f42021fa", "impliedFormat": 1}, {"version": "383b91398f3ad5520d74661409ed1026e553a7ebc607028a8435385ddc8aa0e6", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "eaa2e9af2d32f0586ef50ff5621d3ff94d1e0033639d6b8c84b687bbefbf065f", "impliedFormat": 1}, {"version": "860e2841ceb976416594ea4b428fbfe94a31418f172e27df7926072029034ac3", "impliedFormat": 1}, {"version": "3283decbf4f544bc802a865b08d4d720383bb6ce6e4ed60e013bb40c6bbb6b90", "impliedFormat": 1}, {"version": "f2e47827db13f95a20139b9be29e58b728e8e18f3f86289fa1e99589313e96f5", "impliedFormat": 1}, {"version": "bd8d46dc9e5cfff76b40e8b418ef1e55848366c0ded87cdc06b5a43a9f5e9199", "impliedFormat": 1}, {"version": "55b3a57d0a4e8f6e04b96cb3b9040e3f1bedfba796e072b374110fc832ca706c", "impliedFormat": 1}, {"version": "3a5edc4c3a85102031e36f88ac0149a3c1599c3acabd3a8d62abdc767c96d468", "impliedFormat": 1}, {"version": "5fa0dc3c25e77dc8488194dde96c862fb6df566c6bfc747b4dc8328c8551dc4a", "impliedFormat": 1}, {"version": "e00f958278e17b33bea65dc9954e67b8cab7c47894fbf04afd7465e56283ddb8", "impliedFormat": 1}, {"version": "926b74b0bfae3c43ea8cb9110f4036bbb48c6937593a82a2a29af0d7b027a292", "impliedFormat": 1}, {"version": "3655183a607c5605e52f9d2acb9afcefd8140cfcc21d25345608a038bb4dd93b", "impliedFormat": 1}, {"version": "a32f3a86595a70182a2801759b9c0287c16e8887d9a1c3b3bd19845fe185aae1", "impliedFormat": 1}, {"version": "f5defd0791f7cb141bcc82dcca4bf52f90fcfe43d318ce818bb0a05e21aa8e68", "impliedFormat": 1}, {"version": "85b97ce0065e4c3bde88ef39bbd071e8d7c650cd4d1c1126263ff159c30367fb", "impliedFormat": 1}, {"version": "a26b1facfea6b62ce9ec55106d84fefd1300bdda27ba5ee2bd5ebb676135f644", "impliedFormat": 1}, {"version": "5a9fb12cf4be84597e4bf1750a9dd160205132bfdf5230f9753a73edaf876ae6", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "55245877be1a89483a0e3dc347a17652c034f44b10c3018a62ac8766b7401918", "impliedFormat": 1}, {"version": "e53a3c2a9f624d90f24bf4588aacd223e7bec1b9d0d479b68d2f4a9e6011147f", "impliedFormat": 1}, {"version": "31a7b145313880c12dbec66de4d7b8f8b32abc99974af444cad1b39aa22ee360", "impliedFormat": 1}, {"version": "180e5bb39225f4a4d5b76ba6018abb1ac8550cf6d561187b27d17d85943c0804", "impliedFormat": 1}, {"version": "95f7af90a4415d19e1669570d2525b99f8faed7fd9e14f7a29b43dec6d24c74b", "impliedFormat": 1}, {"version": "6362d52572d95519061db0dfd4a4a570503e8e36ac0c4ac9502fa173f70fb9e8", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "88f9f1cfe56e8e4102031351e9a475378348e65b1515f530fc0fa8e661363f07", "impliedFormat": 1}, {"version": "f590da8ae6e0ce1cf67566380658f5495a8929ad55878b405734e5f211b876ba", "impliedFormat": 1}, {"version": "08faa97886e71757779428dd4c69a545c32c85fd629d1116d42710b32c6378bc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6b042aa5d277ad6963e2837179fd2f8fbb01968ac67115b0833c0244e93d1d50", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "3d77c73be94570813f8cadd1f05ebc3dc5e2e4fdefe4d340ca20cd018724ee36", "impliedFormat": 1}, {"version": "23cfd70b42094e54cc3c5dab996d81b97e2b6f38ccb24ead85454b8ddfe2fc4f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3e58c4c18a031cbb17abec7a4ad0bd5ae9fc70c1f4ba1e7fb921ad87c504aca", "impliedFormat": 1}, {"version": "a3e8bafb2af8e850c644f4be7f5156cf7d23b7bfdc3b786bd4d10ed40329649c", "impliedFormat": 1}, {"version": "35ec8b6760fd7138bbf5809b84551e31028fb2ba7b6dc91d95d098bf212ca8b4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "4b87f767c7bc841511113c876a6b8bf1fd0cb0b718c888ad84478b372ec486b1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d04e3640dd9eb67f7f1e5bd3d0bf96c784666f7aefc8ac1537af6f2d38d4c29", "impliedFormat": 1}, {"version": "3c884d9d9ec454bdf0d5a0b8465bf8297d2caa4d853851d92cc417ac6f30b969", "impliedFormat": 1}, {"version": "5a369483ac4cfbdf0331c248deeb36140e6907db5e1daed241546b4a2055f82c", "impliedFormat": 1}, {"version": "e8f5b5cc36615c17d330eaf8eebbc0d6bdd942c25991f96ef122f246f4ff722f", "impliedFormat": 1}, {"version": "f0bd7e6d931657b59605c44112eaf8b980ba7f957a5051ed21cb93d978cf2f45", "impliedFormat": 1}, {"version": "ee1ee365d88c4c6c0c0a5a5701d66ebc27ccd0bcfcfaa482c6e2e7fe7b98edf7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0ada07543808f3b967624645a8e1ccd446f8b01ade47842acf1328aec899fed0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a37b8d00d03f0381d2db2fe31b0571dc9d7cc0f4b87ca103cc3cd2277690ba0", "impliedFormat": 1}, {"version": "71adf5dbc59568663d252a46179e71e4d544c053978bfc526d11543a3f716f42", "impliedFormat": 1}, {"version": "38bf8ff1b403c861e9052c9ea651cb4f38c1ecc084a34d79f8acc6d6477a7321", "impliedFormat": 1}, {"version": "93bd413918fa921c8729cef45302b24d8b6c7855d72d5bf82d3972595ae8dcbf", "impliedFormat": 1}, {"version": "4ff41188773cbf465807dd2f7059c7494cbee5115608efc297383832a1150c43", "impliedFormat": 1}, {"version": "dccdf1677e531e33f8ac961a68bc537418c9a414797c1ea7e91307501cdc3f5e", "impliedFormat": 1}, {"version": "e184c4b8918ef56c8c9e68bd79f3f3780e2d0d75bf2b8a41da1509a40c2deb46", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d206b4baf4ddcc15d9d69a9a2f4999a72a2c6adeaa8af20fa7a9960816287555", "impliedFormat": 1}, {"version": "93f437e1398a4f06a984f441f7fa7a9f0535c04399619b5c22e0b87bdee182cb", "impliedFormat": 1}, {"version": "afbe24ab0d74694372baa632ecb28bb375be53f3be53f9b07ecd7fc994907de5", "impliedFormat": 1}, {"version": "70731d10d5311bd4cf710ef7f6539b62660f4b0bfdbb3f9fbe1d25fe6366a7fa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6b19db3600a17af69d4f33d08cc7076a7d19fb65bb36e442cac58929ec7c9482", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e043a1bc8fbf2a255bccf9bf27e0f1caf916c3b0518ea34aa72357c0afd42ec", "impliedFormat": 1}, {"version": "137c2894e8f3e9672d401cc0a305dc7b1db7c69511cf6d3970fb53302f9eae09", "impliedFormat": 1}, {"version": "3bc2f1e2c95c04048212c569ed38e338873f6a8593930cf5a7ef24ffb38fc3b6", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "f9d9d753d430ed050dc1bf2667a1bab711ccbb1c1507183d794cc195a5b085cc", "impliedFormat": 1}, {"version": "9eece5e586312581ccd106d4853e861aaaa1a39f8e3ea672b8c3847eedd12f6e", "impliedFormat": 1}, {"version": "ba1f814c22fd970255ddd60d61fb7e00c28271c933ab5d5cc19cd3ca66b8f57c", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "295f068af94245ee9d780555351bef98adfd58f8baf0b9dadbc31a489b881f8b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1851a3b4db78664f83901bb9cac9e45e03a37bb5933cc5bf37e10bb7e91ab4eb", "impliedFormat": 1}, {"version": "09d479208911ac3ac6a7c2fe86217fc1abe6c4f04e2d52e4890e500699eeab32", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "12ed4559eba17cd977aa0db658d25c4047067444b51acfdcbf38470630642b23", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3ffabc95802521e1e4bcba4c88d8615176dc6e09111d920c7a213bdda6e1d65", "impliedFormat": 1}, {"version": "4f029899f9bae07e225c43aef893590541b2b43267383bf5e32e3a884d219ed5", "impliedFormat": 1}, {"version": "ae56f65caf3be91108707bd8dfbccc2a57a91feb5daabf7165a06a945545ed26", "impliedFormat": 1}, {"version": "a136d5de521da20f31631a0a96bf712370779d1c05b7015d7019a9b2a0446ca9", "impliedFormat": 1}, {"version": "5b566927cad2ed2139655d55d690ffa87df378b956e7fe1c96024c4d9f75c4cf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "bce947017cb7a2deebcc4f5ba04cead891ce6ad1602a4438ae45ed9aa1f39104", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d3dffd70e6375b872f0b4e152de4ae682d762c61a24881ecc5eb9f04c5caf76f", "impliedFormat": 1}, {"version": "e2c72c065a36bc9ab2a00ac6a6f51e71501619a72c0609defd304d46610487a4", "impliedFormat": 1}, {"version": "d91a7d8b5655c42986f1bdfe2105c4408f472831c8f20cf11a8c3345b6b56c8c", "impliedFormat": 1}, {"version": "616075a6ac578cf5a013ee12964188b4412823796ce0b202c6f1d2e4ca8480d7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e8a979b8af001c9fc2e774e7809d233c8ca955a28756f52ee5dee88ccb0611d2", "impliedFormat": 1}, {"version": "cac793cc47c29e26e4ac3601dcb00b4435ebed26203485790e44f2ad8b6ad847", "impliedFormat": 1}, {"version": "8caa5c86be1b793cd5f599e27ecb34252c41e011980f7d61ae4989a149ff6ccc", "impliedFormat": 1}, {"version": "66025edda646df525014c61c683fbfcb6bdb9c5e29aa36962f631df4ffa4a6fc", "impliedFormat": 1}, {"version": "87e026025909ca24f456dc0ced23bf64faf1a4d265cff54fead220761e08d476", "impliedFormat": 1}, {"version": "17ed71200119e86ccef2d96b73b02ce8854b76ad6bd21b5021d4269bec527b5f", "impliedFormat": 1}, {"version": "5dbf2a502a7fcd85bfe753b585cfc6c9f60294570ee6a18084e574cf93be3fa0", "impliedFormat": 1}, {"version": "1d01f3439581294ab3080c32dfdd20bf0587e9966609fef6c95be71fe313a16a", "impliedFormat": 1}, {"version": "db6d2d9daad8a6d83f281af12ce4355a20b9a3e71b82b9f57cddcca0a8964a96", "impliedFormat": 1}, {"version": "cb4e5ad6d92f34be63d66fbf7a0808d445ab5e96203f54e08d44bbd52c77f024", "impliedFormat": 1}, {"version": "6502e0f0bc9465e541960143ff5616856e4ae3823efd2a031d6b273cbf5db0ed", "impliedFormat": 1}, {"version": "3a8bddb66b659f6bd2ff641fc71df8a8165bafe0f4b799cc298be5cd3755bb20", "impliedFormat": 1}, {"version": "a86f82d646a739041d6702101afa82dcb935c416dd93cbca7fd754fd0282ce1f", "impliedFormat": 1}, {"version": "2dad084c67e649f0f354739ec7df7c7df0779a28a4f55c97c6b6883ae850d1ce", "impliedFormat": 1}, {"version": "fa5bbc7ab4130dd8cdc55ea294ec39f76f2bc507a0f75f4f873e38631a836ca7", "impliedFormat": 1}, {"version": "e5b4cc78550f69ba45664462b28a704897ad84a316807464d384d8d3ca3c8116", "impliedFormat": 1}, {"version": "e96fc8b0f4b5f5a6d07b20941ca0c68b85ab123caa13f9dd11da65f06f77d7ba", "impliedFormat": 1}, {"version": "196c960b12253fde69b204aa4fbf69470b26daf7a430855d7f94107a16495ab0", "impliedFormat": 1}, {"version": "ebd4f9da713970ca07c8345dd656e11657177b4504140eef4f21673d64b6079b", "impliedFormat": 1}, {"version": "bf24f6d35f7318e246010ffe9924395893c4e96d34324cde77151a73f078b9ad", "impliedFormat": 1}, {"version": "6173223c35a6239946c78c2dcff94edee03997e6382e7f5d3a70d5a7a4a8d350", "impliedFormat": 1}, {"version": "28cd5d472951def47e0cdb73b2b3dd85f59d6ef579a4de07dd4f4a9316a06224", "impliedFormat": 1}, {"version": "5be374945203bfee698c503d1bc2771cb396c5d19420faccf44a15bc48c9d740", "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "impliedFormat": 1}, {"version": "a9af0e608929aaf9ce96bd7a7b99c9360636c31d73670e4af09a09950df97841", "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "impliedFormat": 1}, {"version": "c86fe861cf1b4c46a0fb7d74dffe596cf679a2e5e8b1456881313170f092e3fa", "impliedFormat": 1}, {"version": "abde5f4e41dcac96e28128f2e91b069339445799228835aa0648326a04701cf9", "impliedFormat": 1}, {"version": "c8ed383401edad4207c3290b95064a5cba32454c47bb19d54123e451f02c186f", "impliedFormat": 1}, {"version": "b28f46e10d2fea373fa5a2582bd681659a31b6f44372bfad8966624af9b06575", "impliedFormat": 1}, {"version": "10cf643ec50d26c16cba4b2b00042f9f1bb83732ffd8dfa708154e1cd6bb6146", "impliedFormat": 1}, {"version": "47105af2053eda36d79c9f6101d246552138082554136762cfb54be81d096881", "impliedFormat": 1}, {"version": "4dc851ddb50b2f0d91f1d76ae6d4ef1f2d157214f90612bc4f38d744597a5548", "impliedFormat": 1}, {"version": "fc3e1c87b39e5ba1142f27ec089d1966da168c04a859a4f6aab64dceae162c2b", "impliedFormat": 1}, {"version": "3b414b99a73171e1c4b7b7714e26b87d6c5cb03d200352da5342ab4088a54c85", "impliedFormat": 1}, {"version": "cb4b4ec9c66d5ef94b6f4270831795e22e88c23b9011c2e7acceed56c1c95abc", "impliedFormat": 1}, {"version": "459b1aef3c66bede7364ad6a857212a1ea8c692c8e1630797d62f7fb993b8c98", "impliedFormat": 1}, {"version": "691dcc4ec0b25e1267a4234e21e2c341e16cbbad196630665bc5f92f886021b5", "impliedFormat": 1}, {"version": "d0f042ee11743179da949270a480b0cef6646741a43a0d01bc742f8c91b42353", "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "impliedFormat": 1}, {"version": "aa9b9973345022020ca15c04c4e2789e9d3f31bd30cc0355bde1a8e667c866c2", "impliedFormat": 1}, {"version": "b7d110fec96a41033b9dbe3fb54907154332ad8753ddebc18c586abef96e05cd", "impliedFormat": 1}, {"version": "ac60bbee0d4235643cc52b57768b22de8c257c12bd8c2039860540cab1fa1d82", "impliedFormat": 1}, {"version": "6428e6edd944ce6789afdf43f9376c1f2e4957eea34166177625aaff4c0da1a0", "impliedFormat": 1}, {"version": "f9c80a4247a63a7e45ec3f782602fa6b90f1d15e369bf0a6cb1a7c05c4fbd8f3", "impliedFormat": 1}, {"version": "6e5f5cee603d67ee1ba6120815497909b73399842254fc1e77a0d5cdc51d8c9c", "impliedFormat": 1}, {"version": "9792277795fa4342d163761c1ce4042c92dca9d4bf5e9a610bcc87e88831413d", "impliedFormat": 1}, {"version": "70f3814c457f54a7efe2d9ce9d2686de9250bb42eb7f4c539bd2280a42e52d33", "impliedFormat": 1}, {"version": "154dd2e22e1e94d5bc4ff7726706bc0483760bae40506bdce780734f11f7ec47", "impliedFormat": 1}, {"version": "6a6e3c6a0b6d853043fe0c5a7381f7a18b51581fb2c21c52d814167f650a1a30", "impliedFormat": 1}, {"version": "9c82171d836c47486074e4ca8e059735bf97b205e70b196535b5efd40cbe1bc5", "impliedFormat": 1}, {"version": "f95e0747b1656fe12d17dc4697cee3d01b38159409e27e7fc38e05a0304f0a26", "impliedFormat": 1}, {"version": "50bdf521a55df156c1d27d4d91da34906da201eb2f7f120c0e168140d1d0be4f", "impliedFormat": 1}, {"version": "c5426dbfc1cf90532f66965a7aa8c1136a78d4d0f96d8180ecbfc11d7722f1a5", "impliedFormat": 1}, {"version": "db0dc38b6dae1393ba45bede54645a9791a278164f920bee8a3f719130e0f3b9", "impliedFormat": 1}, {"version": "f35e51941561b3672a78f312ca51f7c7f166e1bba7069923025bf11d32851096", "impliedFormat": 1}, {"version": "de9d2df7663e64e3a91bf495f315a7577e23ba088f2949d5ce9ec96f44fba37d", "impliedFormat": 1}, {"version": "0f78fe0aa778f04e5fe30f0be86da7a1a8a0d7853ed3f87f4f6527a74d16ce51", "impliedFormat": 1}, {"version": "039706c119c1e5a7f8a8e0ff24690546aacdc43d6acc42ea2d237628501f0969", "impliedFormat": 1}, {"version": "f7798e024c36caa309fdf1f4b5aed17c9368b5b07495adabf939ca0756243086", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b5c5ba3e0bd0e03b527a95bf9158fc90d4127d55ca6652099b3d42c865d336c7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dba114fb6a32b355a9cfc26ca2276834d72fe0e94cd2c3494005547025015369", "impliedFormat": 1}, {"version": "df306e3e18255b3938e265c50d3397748084c4ecb9f7ffefd87b2ef2c2ac6565", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "41f57fda27d27366470b6f95a47940226c170e78e8cf1f7b0ca2e82ed0d1c510", "impliedFormat": 1}, {"version": "f1c32f9ce9c497da4dc215c3bc84b722ea02497d35f9134db3bb40a8d918b92b", "impliedFormat": 1}, {"version": "b73c319af2cc3ef8f6421308a250f328836531ea3761823b4cabbd133047aefa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f09954eb17ae3c5f5bef10e1a32942ea02a19f834de8db279760fae0d61f2a11", "impliedFormat": 1}, {"version": "16f937a7d60b05ebf14c2f1cb7e81de4c83748ad587c34213c9f47dfce0cf0a3", "impliedFormat": 1}, {"version": "ddff7fc6edbdc5163a09e22bf8df7bef75f75369ebd7ecea95ba55c4386e2441", "impliedFormat": 1}, {"version": "bb868cb23c0d8cc6f09243aa5d0ef5f835756222511807aba9fb2aeaebcf315b", "impliedFormat": 1}, {"version": "ec29be0737d39268696edcec4f5e97ce26f449fa9b7afc2f0f99a86def34a418", "impliedFormat": 1}, {"version": "265814459e8d30151a838c3cee3124dc48e24150a726e7173ea5809a8c27a0a3", "impliedFormat": 1}, {"version": "ec6cba1c02c675e4dd173251b156792e8d3b0c816af6d6ad93f1a55d674591aa", "impliedFormat": 1}, {"version": "b620391fe8060cf9bedc176a4d01366e6574d7a71e0ac0ab344a4e76576fcbb8", "impliedFormat": 1}, {"version": "4ed0cf4ef9e92caf6168dabeb840bf47cf876d16fa94dd814737c5af284bb440", "impliedFormat": 1}, {"version": "607ff06104eb4b794a1c50d94c546a93889c36870f50216c6635349220cf4c02", "impliedFormat": 1}, {"version": "ea86daad73504a0fa55e7c7a10b4c69cc1918bd7382ce28f2f2d08367e7bacf8", "impliedFormat": 1}, {"version": "c17e5859b666d71007ecf4635622a172e0bf594c55977efbb78e7be3af9e0b0b", "impliedFormat": 1}, {"version": "5e7306b84dd025888abe5d330cc25a5d245afd6347cc321ca10c9f285751db81", "impliedFormat": 1}, {"version": "43ba4f2fa8c698f5c304d21a3ef596741e8e85a810b7c1f9b692653791d8d97a", "impliedFormat": 1}, {"version": "e14ce7e33827e8f5b8318aa61dab2109b0fe84341b4b2164f0903008fc9c68fe", "impliedFormat": 1}, {"version": "2b5ee06c5b0c59d7282e8d7600374781b17d00d3b069ecaf9aab1207bf3d3118", "impliedFormat": 1}, {"version": "9005fa9a8857a53ac46317498362e42a6661deeede458f386a08d49b969ccfef", "impliedFormat": 1}, {"version": "11d21397e98ddb68b15bbd2f6a31d04d011cac2ea63cef1a19950ad1db2007ff", "impliedFormat": 1}, {"version": "15c98bb9dc05847d0895a0e39649edaa7b4e7f4e0abaa0b67bf5cff3cfb264e3", "impliedFormat": 1}, {"version": "48e8af7fdb2677a44522fd185d8c87deff4d36ee701ea003c6c780b1407a1397", "impliedFormat": 1}, {"version": "baa42884a86483ffa3466ca16d33685f2ed4d988cfb23a18d107ae26458ff4a7", "impliedFormat": 1}, {"version": "05295051648e755e3d273c26544f385fe864fd0dec188da9ced38f524437855a", "impliedFormat": 1}, {"version": "f9812cfc220ecf7557183379531fa409acd249b9e5b9a145d0d52b76c20862de", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "512fe3bf728765999bd5cdfb1cf7a9017c79c8d27e607e751f16d44b3721065a", "impliedFormat": 1}, {"version": "2e4f37ffe8862b14d8e24ae8763daaa8340c0df0b859d9a9733def0eee7562d9", "impliedFormat": 1}, {"version": "13283350547389802aa35d9f2188effaeac805499169a06ef5cd77ce2a0bd63f", "impliedFormat": 1}, {"version": "20276824b9d93872ba1dc9d39ab1a306b062a69781f29017bf78a4fdc1e38c82", "impliedFormat": 1}, {"version": "1d0ad295cbb065aec59026556a80991bf278998ad613c7ebd4f5d624f1c79b9f", "impliedFormat": 1}, {"version": "6f560e66c0651beca4d0c80a94e3804d8b4b8b904fffb8869a2f3e7632302c85", "impliedFormat": 1}, {"version": "913ddbba170240070bd5921b8f33ea780021bdf42fbdfcd4fcb2691b1884ddde", "impliedFormat": 1}, {"version": "21b4e72167ba0e68c6d3e2ac1f0d7647aff958063c00771745924424498788c2", "impliedFormat": 1}, {"version": "4501ed10e7c964e804e2e863cb101e5f5a575c1ed104ba30d94e484531f57103", "impliedFormat": 1}, {"version": "fd1685c70a088e2a8f75473bb0ffee1f50eec96e2fb5bbef2f6d37c0c8ed7b99", "impliedFormat": 1}, {"version": "438c7513b1df91dcef49b13cd7a1c4720f91a36e88c1df731661608b7c055f10", "impliedFormat": 1}, {"version": "2879b48dc39e4c557ec4ba8437cadfaf47067b15cd06d138759bce0091e179ee", "impliedFormat": 1}, {"version": "0d223620804e69833f36173135f422102c4a5fcd0c2b66dd0c1ca76e20a2158e", "impliedFormat": 1}, {"version": "efaa078e392f9abda3ee8ade3f3762ab77f9c50b184e6883063a911742a4c96a", "impliedFormat": 1}, {"version": "54a8bb487e1dc04591a280e7a673cdfb272c83f61e28d8a64cf1ac2e63c35c51", "impliedFormat": 1}, {"version": "021a9498000497497fd693dd315325484c58a71b5929e2bbb91f419b04b24cea", "impliedFormat": 1}, {"version": "9385cdc09850950bc9b59cca445a3ceb6fcca32b54e7b626e746912e489e535e", "impliedFormat": 1}, {"version": "5932c146355509d60a3a42277e4134b84e4407bf80679bd161d769c04b060a76", "impliedFormat": 1}, {"version": "0a72186f94215d020cb386f7dca81d7495ab6c17066eb07d0f44a5bf33c1b21a", "impliedFormat": 1}, {"version": "a712b904f72e940f01d0474013d9f527c90c5085b01106fb3afcb5d58cd4c215", "impliedFormat": 1}, {"version": "d9f68999e2cd3014239cde49267a1628ce2db60372ecab1e3a01417bd0c09196", "impliedFormat": 1}, {"version": "ad84288fb7fc856202d0d2848c5364bed21f13460e87e111d50c9a9e1abd77d1", "impliedFormat": 1}, {"version": "b01bd582a6e41457bc56e6f0f9de4cb17f33f5f3843a7cf8210ac9c18472fb0f", "impliedFormat": 1}, {"version": "440aa061025babc77beac54f4473edcab976eed2e02a6b1f8c4d911fe1f5064d", "impliedFormat": 1}, {"version": "a6d6f33bddcd5fd1f8cbda0a41345b832d82fe13302afadc85d4a663618dc8ef", "impliedFormat": 1}, {"version": "44186f1886efdaf6836f153e40e4859193a08f7ea540c4796ecdd7cd9d3f9328", "impliedFormat": 1}, {"version": "60c9e1ff617bd0eaa0eaa8b7b5b16c88e3cd21d1fa1144a9ddd2ecacc61b0d7c", "impliedFormat": 1}, {"version": "275d9c2eb0bd31a7dfadc6956224d1145642b476c35b6659e22bd6367abb6529", "impliedFormat": 1}, {"version": "79cf2cd47f90116a068920cb26c6bf93290630aef8acb3cae240552cd087736c", "impliedFormat": 1}, {"version": "7e5f36c3a75175205d8a93ed51f378e0df9932421debec258b8431143d50848b", "impliedFormat": 1}, {"version": "0b96d66a0c2b900239949de997b010ad735cf1c526bb462e80eff39abe663671", "impliedFormat": 1}, {"version": "fc249806dc5d7d7005adf74389ce6376f3ee56990f08c570458ae78bdafb0996", "impliedFormat": 1}, {"version": "2c99e7a092199fb30d52603086fe81db3ec7ba88a3470fc215e86b98d532283d", "impliedFormat": 1}, {"version": "2233d14eb0a1d6bc2f218d8f144745f4c79b3ebb0567c95af44e3ac8c405359c", "impliedFormat": 1}, {"version": "7f5d32b48c8a02f8c83070c2de51ec0570efd727802d0d9e16801a587c09927d", "impliedFormat": 1}, {"version": "9ac8cb3bd1ff8dd0c8164a5ac6a4243198051b18fe0b7239e692aa89a91ecd5e", "impliedFormat": 1}, {"version": "7a75b6231d8398d5817d22ba11b8e151590c78c3b1059d871fb718696a5bb9b4", "impliedFormat": 1}, {"version": "e927c2c13c4eaf0a7f17e6022eee8519eb29ef42c4c13a31e81a611ab8c95577", "impliedFormat": 1}, {"version": "5a541a5fc0ac7717e71fea077d2461316d795c1fb2c28fa1e0a9f91940822032", "impliedFormat": 1}, {"version": "7fd15bac0e2373dc786f2d3c0f08093d25bdb129a5ba8551fa990c3da827c31f", "impliedFormat": 1}, {"version": "da9ccbd7996178ddadd58a6912b04a504be4cb7f41678ebfc721bd7038e01fb9", "impliedFormat": 1}, {"version": "d0590f15b62294202e79d5058ea030f544e8097f4ed87be717213e65da79639d", "impliedFormat": 1}, {"version": "4fe5679d7215bd674a4fc9f857e23393b7e89870f3e607727d1a72bcf19cfc12", "impliedFormat": 1}, {"version": "ea597c121a7e63f86ce3af453114ba1b8b730f3539225bbef421d9cf3394e68f", "impliedFormat": 1}, {"version": "d60436a5113ffbecc5936af57b8f62bf49dcfee7e14f5922fde1612749fc4b68", "impliedFormat": 1}, {"version": "5174a2a11d6d7b33cb597a41fc290ce99ad1353a5678d266cfd515f0ddcf7c6e", "impliedFormat": 1}, {"version": "fced48e3ce35fcc91c0ad95e180bb82cb0ce3edda78d4ae33ee77deea98c0b48", "impliedFormat": 1}, {"version": "1e6f50d824519604113d6b7f6872240664c8b2e47b23389984319e821dfb4e87", "impliedFormat": 1}, {"version": "bb96e56d8afa389e8e3e449219e45423e7852c71252debc491676cffd439ffe7", "impliedFormat": 1}, {"version": "097f052e1eb6d53272d8cdd396c7531954d4ac3e78973c534176b52408bddadb", "impliedFormat": 1}, {"version": "6459054aabb306821a043e02b89d54da508e3a6966601a41e71c166e4ea1474f", "impliedFormat": 1}, {"version": "f416c9c3eee9d47ff49132c34f96b9180e50485d435d5748f0e8b72521d28d2e", "impliedFormat": 1}, {"version": "05c97cddbaf99978f83d96de2d8af86aded9332592f08ce4a284d72d0952c391", "impliedFormat": 1}, {"version": "6c966aeb9d90d2341914ca0a509fdaf8de62d59e75e46dad9e429b12c0db0c7f", "impliedFormat": 1}, {"version": "bbc183d2d69f4b59fd4dd8799ffdf4eb91173d1c4ad71cce91a3811c021bf80c", "impliedFormat": 1}, {"version": "15c9ecf0d4ad595549b1645786ece24bc0ec01ee5bc867112673503d6baf4409", "impliedFormat": 1}, {"version": "9f213acaf71f78157b8af596b45827c2790334c5468c605dee12b6209732d8d9", "impliedFormat": 1}, {"version": "c2f2844fb78ed723a03b846bc3406b5ef32ae7746f5a332001d39cb011a1f37a", "impliedFormat": 1}, {"version": "a7db259f819f24477b3d8f6aa8f89c3e56b47d34a68c141597acaaab3e27f843", "impliedFormat": 1}, {"version": "cfea5892a0ac990695f20039850671974e74f595fc7c1ff97d2afe84994fa0d9", "impliedFormat": 1}, {"version": "f974e4a06953682a2c15d5bd5114c0284d5abf8bc0fe4da25cb9159427b70072", "impliedFormat": 1}, {"version": "3364965b66ae42309179b2a4493162ccf0b11d83767b9e7c4d7713253d126860", "impliedFormat": 1}, {"version": "94404c4a878fe291e7578a2a80264c6f18e9f1933fbb57e48f0eb368672e389c", "impliedFormat": 1}, {"version": "5c1b7f03aa88be854bc15810bfd5bd5a1943c5a7620e1c53eddd2a013996343e", "impliedFormat": 1}, {"version": "4eb7c3ca811f7890d032535066bc396c93f147596398f7ee5dbea388d57851a1", "impliedFormat": 1}, {"version": "16781f4ce4fcf8c6cecb46816209f9088a812ce2c1734d17c2eb8d060c2b7639", "impliedFormat": 1}, {"version": "01aa917531e116485beca44a14970834687b857757159769c16b228eb1e49c5f", "impliedFormat": 1}, {"version": "fa49815c964a76b569535baaa34d928aac088c993845b239b1640274f5cc7f26", "impliedFormat": 1}, {"version": "86f8b8a0f57be20bdcb6e2c9fb9333a6f158fcb5f80b5a7500a26e906afc28f3", "impliedFormat": 1}, {"version": "572db26218cb56077f845bbadae4650f551bd5b508a66339f750ebf64bc806b1", "impliedFormat": 1}, {"version": "518ff31c7bb4c432771c902d62fd53c4a8acde7a515a8f5258c50b37a9e11782", "impliedFormat": 1}, {"version": "0f6bfd2e195fe5d0841f2606c292694f4f096098f40e1a977fbe9950cfa882f3", "impliedFormat": 1}, {"version": "f285a31711f6b42acea7df89bf54237a60a1ae92ea8151335990b6a81a5c8b28", "impliedFormat": 1}, {"version": "f8acecec1114f11690956e007d920044799aefeb3cece9e7f4b1f8a1d542b2c9", "impliedFormat": 1}, {"version": "178071ccd043967a58c5d1a032db0ddf9bd139e7920766b537d9783e88eb615e", "impliedFormat": 1}, {"version": "3a17f09634c50cce884721f54fd9e7b98e03ac505889c560876291fcf8a09e90", "impliedFormat": 1}, {"version": "32531dfbb0cdc4525296648f53b2b5c39b64282791e2a8c765712e49e6461046", "impliedFormat": 1}, {"version": "69b1a182b8ebe9c55a571f8b3b689a340ebc5f247e204a4571aaebbd063bfac8", "impliedFormat": 1}, {"version": "3c11e801b4289d1f59c2dc1a99b51a5fa82b8c28ad5eba133cb2ff5e0146f553", "impliedFormat": 1}, {"version": "5dda806aef526a07c6c4a1469f09526a06bfee060d3523339188b2b38c71ea9d", "impliedFormat": 1}, {"version": "2c2698ac3b4e71edf9a418beb8dc3696ef7e1d8bb7f6d0713bd4c29990334be1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e20764360854ecfad8caafdcb8fd0b0b8653d96bb760a51ccf3e0adfc38e4696", "impliedFormat": 1}, {"version": "19252079538942a69be1645e153f7dbbc1ef56b4f983c633bf31fe26aeac32cd", "impliedFormat": 1}, {"version": "76adeb87287afb30a840138a18ffdc90e41ac46c12c71cd163065dbb3b55206f", "impliedFormat": 1}, {"version": "12dfeac15bfad3f8bc83bafb3b3beb735cb24bdac841fc4829727e3caf6a0100", "impliedFormat": 1}, {"version": "acca105d5cba5511f0381ec590ce4c5fd2c4e8812196e8755b33b9901cbf799a", "impliedFormat": 1}, {"version": "f9fe6af238339a0e5f7563acee3178f51db37f32a2e7c09f85273098cee7ec49", "impliedFormat": 1}, {"version": "3b0b1d352b8d2e47f1c4df4fb0678702aee071155b12ef0185fce9eb4fa4af1e", "impliedFormat": 1}, {"version": "77e71242e71ebf8528c5802993697878f0533db8f2299b4d36aa015bae08a79c", "impliedFormat": 1}, {"version": "a344403e7a7384e0e7093942533d309194ad0a53eca2a3100c0b0ab4d3932773", "impliedFormat": 1}, {"version": "b7fff2d004c5879cae335db8f954eb1d61242d9f2d28515e67902032723caeab", "impliedFormat": 1}, {"version": "84b7e6ee9ad69164c400ee5ba85032f3250128b752f471578c613615ba946de7", "impliedFormat": 1}, {"version": "ff61de122076f9f49230803cbdd4fbb0bb06bc6c935f63dc06e342aec7ea2389", "impliedFormat": 1}, {"version": "266d15e8d95639e96f3881b0f9e0eca30680a51f624c7e91a3c7adaba04ab0f7", "impliedFormat": 1}, {"version": "e9b6fc05f536dfddcdc65dbcf04e09391b1c968ab967382e48924f5cb90d88e1", "impliedFormat": 1}, {"version": "7530e7c0e6cc54ffb515e770bd7db72790327bbe2abb626b6eae98f875bbb37a", "impliedFormat": 1}, {"version": "60a134bf253fc8f2fd5169febe21c4a83da26e58d368722915de758c7c37d4d2", "impliedFormat": 1}, {"version": "1ef6057a706459c4ee22ab0820521469da65059b8be4c377c443678f1f032a2c", "impliedFormat": 1}, {"version": "3cd8f0464e0939b47bfccbb9bb474a6d87d57210e304029cd8eb59c63a81935d", "impliedFormat": 1}, {"version": "37980e4b9dd5f50d12258d9b03ab7a7926dd332a8394e0e0bb004a6f25f59f04", "impliedFormat": 1}, {"version": "c386502d1c617109edf8304824e9b351d4e0d3df3e69dc2244c8ddc4fb8310f1", "impliedFormat": 1}, {"version": "a85ea9d8c67da0d02ae6140c1bcd2641771e57bada0e1282a74d1afdff17784d", "impliedFormat": 1}, {"version": "35ce0ba10a47ef7c6f06273c58e47b4672ff27546e80046e052b096b919e400c", "impliedFormat": 1}, {"version": "fd49219eb89f1f8a9b6df83607edfa98684e9fd0ae6f808234573b3c8bea3450", "impliedFormat": 1}, {"version": "91a75c865f532e4e9f2836ef276d833ed00c43e789ad45595cafa9a3728753e5", "impliedFormat": 1}, {"version": "70467e1c02aa00052c3d985977b149d913ccfe6d45d250fec2a4186afb5701d4", "impliedFormat": 1}, {"version": "832cc052141797db5b63a82541f63bc82f2ca24010332918e6ab72acbfbd236c", "impliedFormat": 1}, {"version": "0d28b974a7605c4eda20c943b3fa9ae16cb452c1666fc9b8c341b879992c7612", "impliedFormat": 1}, {"version": "633cadaf007b682fa84970b52d2d702edc93a016b612d546bf1d932e3056f7c9", "impliedFormat": 1}, {"version": "db036c56f79186da50af66511d37d9fe77fa6793381927292d17f81f787bb195", "impliedFormat": 1}, {"version": "68fa3dabc4cc6a9e14d32652049629cff74daf6b49b6248ffb07e6091108ca58", "impliedFormat": 1}, {"version": "13c8b4348db91e2f7d694adc17e7438e6776bc506d5c8f5de9ad9989707fa3fe", "impliedFormat": 1}, {"version": "842ef68d871344373ed0b1b6ffdcb7a53ce05ba3012d200607551575dfeb11f7", "impliedFormat": 1}, {"version": "b928c3c32e95724a699b3405aee07130c2962f5b06717a13d32ac05b07cf36f8", "impliedFormat": 1}, {"version": "353cdde79e62499e78eca46c79e6dbd6687a2675313381bc2e5268839fbb3cab", "impliedFormat": 1}, {"version": "6a421e41d2c390657b9c78a5a9a1da2c95c424239d3eb328f9f60bc173365259", "impliedFormat": 1}, {"version": "4cd4b6b1279e9d744a3825cbd7757bbefe7f0708f3f1069179ad535f19e8ed2c", "impliedFormat": 1}, {"version": "38ba9d81e0724de56d30acc72a8b856e2c0125795c683bc69daa8e0d9f3d5a3c", "impliedFormat": 1}, {"version": "c0eeaaa67c85c3bb6c52b629ebbfd3b2292dc67e8c0ffda2fc6cd2f78dc471e6", "impliedFormat": 1}, {"version": "4b7f74b772140395e7af67c4841be1ab867c11b3b82a51b1aeb692822b76c872", "impliedFormat": 1}, {"version": "27be6622e2922a1b412eb057faa854831b95db9db5035c3f6d4b677b902ab3b7", "impliedFormat": 1}, {"version": "b95a6f019095dd1d48fd04965b50dfd63e5743a6e75478343c46d2582a5132bf", "impliedFormat": 99}, {"version": "c2008605e78208cfa9cd70bd29856b72dda7ad89df5dc895920f8e10bcb9cd0a", "impliedFormat": 99}, {"version": "4d74fea1ff0dfe41e904f5efa1e987b3d353cac816e5a5171866b155fbac0568", "impliedFormat": 99}, {"version": "d23df9ff06ae8bf1dcb7cc933e97ae7da418ac77749fecee758bb43a8d69f840", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "040c71dde2c406f869ad2f41e8d4ce579cc60c8dbe5aa0dd8962ac943b846572", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3586f5ea3cc27083a17bd5c9059ede9421d587286d5a47f4341a4c2d00e4fa91", "impliedFormat": 1}, {"version": "b072fc879b7cfe27951f787b45cf213bb762e70f300bd8c0b0bb606e071e0be8", "impliedFormat": 1}, {"version": "ca58cb2def0f213ee685384b5e089f0d66269c9dc61df8fc070d18d7a32e69f6", "impliedFormat": 1}, "daeb16c108ebc4ae4551a4e71cf50ab66430b0908d8637d9e3f08122ca030ba0", {"version": "cf5e81f38783891e4b710230efa47f40f66ae5bc8f59a3b81c7b90e513bf31a2", "impliedFormat": 1}, {"version": "71df692065d79c5b044002f8a68eba33e934859859094bab2f334f64a467f428", "impliedFormat": 1}, {"version": "51ebca098538b252953b1ef83c165f25b52271bfb6049cd09d197dddd4cd43c5", "impliedFormat": 1}, {"version": "60b279f3e8e173f88af9ae6a7ce774381479c87c8aa1b96565578719b477749f", "signature": "8fe3d7009ea7f851b8bf4e39b4067c9feaaeb90458929686a4aa4fc3544ce8d8"}, {"version": "158e3731c88667eabd94eb7aef17a045adedcb97be48c51176870d7273314b6e", "signature": "6b205cf472d092fd5d3dd0f9b6bf63e75956aaf8cd802e41251bd473596c172d"}, {"version": "fa7edf9e319f80cba828a86ebc861ca20c57d09336309daa5d20d035a52c94dd", "signature": "0afff13911ad6dfa2545901a2cc4be734197ac2b859df6f554350b9206fd657f"}, {"version": "cf89d974d3f112980865e38287355e3f4b8edcbb0dda6feb1aec885ffdf360f9", "signature": "ae1e595b0de2bbe2ebb69bb22d4bd227f5c41f3b2f49a7442dd2da57dbca1905"}, {"version": "eb23d906bbbee0efb41c4bae3c1d86873306e2feeb83ebbac8ae11e6500d344f", "signature": "69db1cf9b6b2fa49eb2895abc552b47ff5386b8fbc8707b5d56a63df53212a87"}, {"version": "ebd2dffb3b026278f51e7ad0be2759f7766c93a15042324986a10521e690c6dc", "signature": "188a18cca9d8f1e58584e5d3f43cc5e51b71ce7b5d754719db8b077b26ff99bd"}, {"version": "eb27786151c4d688a692ff75d2cbd02e8a4b20a36f21db3ac8a2678d084777d3", "signature": "b289c8cbf43d59c256bbaf557c39a4458ba6adc649590e6d7448f7468aafa1b2"}, {"version": "98694a8450430862a5de095fbd7bbb6ce169981c369aba74c6ff8c4e6bdfba32", "signature": "f565b26cdcd80d75d750df80aefe2e0b5b49fbf3ee311e68cc76affd9d29f83a"}, {"version": "255dd447102850244625f4b33fba8a7585396fbb5ad884aaccdf224028cf0695", "signature": "26a00369669bff99146a5fb455bf821fcd7b211d34eddbd7477ba5349077922a"}, {"version": "3fdb837217c8df4be4bfc06066436ddd638e8d610b849260b7af261cd04d1b15", "signature": "8dd2796740bb2a9010b22d95d4d73b63ffd482dd11b3d9a0473d00a87115dea7"}, {"version": "78d3196dff1bb70e2e9a3ef33c6a012ded73f3328fdd9c753b0079c53880d495", "signature": "3344f2d2fbc00aff6913c1b65c00607cbc2e19d801d6983db4038c54076a106f"}, {"version": "160b24efb5a868df9c54f337656b4ef55fcbe0548fe15408e1c0630ec559c559", "impliedFormat": 1}, {"version": "55535a226d481c9acabc3801447279bdb9020c50dd02ed0cd1f1d45fcf4ea32b", "signature": "6a3800c48887fa587f2460ecd224cd0fff15be03eff847503deb6dab5c3050dc"}, {"version": "e241e2a2e6f4614b76add6d069532ff21988f077bbf31f78fa55d1e48c2a9a68", "signature": "61c2c12f2354aec0bfda4f3ae6e498f9dfa5198b159d44785c34292745c2c142"}, {"version": "169f0b5281b33cf5cc272b4331065c3f467afe3f3622d1bf414e416e9b0c6ff3", "signature": "61c2c12f2354aec0bfda4f3ae6e498f9dfa5198b159d44785c34292745c2c142"}, {"version": "b1535397a73ca6046ca08957788a4c9a745730c7b2b887e9b9bc784214f3abac", "impliedFormat": 1}, {"version": "1dab12d45a7ab2b167b489150cc7d10043d97eadc4255bfee8d9e07697073c61", "impliedFormat": 1}, {"version": "611c4448eee5289fb486356d96a8049ce8e10e58885608b1d218ab6000c489b3", "impliedFormat": 1}, {"version": "5de017dece7444a2041f5f729fe5035c3e8a94065910fbd235949a25c0c5b035", "impliedFormat": 1}, {"version": "d47961927fe421b16a444286485165f10f18c2ef7b2b32a599c6f22106cd223b", "impliedFormat": 1}, {"version": "341672ca9475e1625c105a6a99f46e8b4f14dff977e53a828deef7b5e932638f", "impliedFormat": 1}, {"version": "d3b5d359e0523d0b9f85016266c9a50ce9cda399aeac1b9eeecb63ba577e4d27", "impliedFormat": 1}, {"version": "5b9f65234e953177fcc9088e69d363706ccd0696a15d254ac5787b28bdfb7cb0", "impliedFormat": 1}, {"version": "510a5373df4110d355b3fb5c72dfd3906782aeacbb44de71ceee0f0dece36352", "impliedFormat": 1}, {"version": "137272a656222e83280287c3b6b6d949d38e6c125b48aff9e987cf584ff8eb42", "impliedFormat": 1}, {"version": "970e51f97fa0ec3a8d7ab6919b8a6dbfac85cd08f53c3b01b4181c0ac4fc4fcf", "impliedFormat": 1}, {"version": "c699deadc53cf0599eb629439d2aadbe430c3af73d7d1439a7b0b6718b36f05d", "impliedFormat": 1}, {"version": "0139619803f70a9a55e83b4421b3c92e4c6e4e9e5ad5867896bde9cd05f58aec", "impliedFormat": 1}, {"version": "e4526a74e1b4b99d1ea9343b1bd656c2b90616f3b7361e53b119bc7005e83151", "impliedFormat": 1}, {"version": "5277b2beeb856b348af1c23ffdaccde1ec447abede6f017a0ab0362613309587", "impliedFormat": 1}, {"version": "d4b6804b4c4cb3d65efd5dc8a672825cea7b39db98363d2d9c2608078adce5f8", "impliedFormat": 1}, {"version": "929f67e0e7f3b3a3bcd4e17074e2e60c94b1e27a8135472a7d002a36cd640629", "impliedFormat": 1}, {"version": "72e42613905a0f9c15ba53545a43c3977ade8eda72dfb4352f15aa2badfe6bf8", "impliedFormat": 1}, {"version": "14b3ff88d8ab0d33c3f5da5bb25ee77fa6b47698394be7f2eae7e66830bf1fed", "impliedFormat": 1}, {"version": "e518732b8eaeefaf81dd29faa3e4e7236ff4ac2a8ae69b2464b70f62a72ee323", "impliedFormat": 1}, {"version": "45079ac211d6cfda93dd7d0e7fc1cf2e510dad5610048ef71e47328b765515be", "impliedFormat": 1}, {"version": "1c19f268e0f1ed1a6485ca80e0cfd4e21bdc71cb974e2ac7b04b5fce0a91482b", "impliedFormat": 1}, {"version": "c27ee6ee31641dfd4968d11c250aad4f50a106a6eb578a2b2c751363dce289ce", "impliedFormat": 1}, {"version": "4d61e28aec3531908a7a4974c769b7469726c657192eb87844b7f7239432c45b", "impliedFormat": 1}, {"version": "5dcc7e2f30e488403cc48a165e4cd266c8b4e7650f349eaa3a642e91f5d14d08", "impliedFormat": 1}, {"version": "ba64b14db9d08613474dc7c06d8ffbcb22a00a4f9d2641b2dcf97bc91da14275", "impliedFormat": 1}, {"version": "530197974beb0a02c5a9eb7223f03e27651422345c8c35e1a13ddc67e6365af5", "impliedFormat": 1}, {"version": "fbee981272d8d1549f47e60661c1a25235e847229655265b69cbec32af767375", "impliedFormat": 1}, {"version": "98e36c52f74cde5bf2a7438ee0d6ed311397902b4bf4399c54f74aca07b5dd82", "impliedFormat": 1}, {"version": "19d04b82ed0dc5ba742521b6da97f22362fe40d6efa5ca5650f08381e5c939b2", "impliedFormat": 1}, {"version": "f02ac71075b54b5c0a384dddbd773c9852dba14b4bf61ca9f1c8ba6b09101d3e", "impliedFormat": 1}, {"version": "bbf0ae18efd0b886897a23141532d9695435c279921c24bcb86090f2466d0727", "impliedFormat": 1}, {"version": "26c7a304fb917794c9bfd02326c542e4eebebf6909dc072bbe9501715bb18356", "impliedFormat": 1}, {"version": "f94c2a1593fbe4acaa29785e5d03a594910dea4b3efb11f8b80948285e198c90", "impliedFormat": 1}, {"version": "1bbc5664ade7b2b229f6454485d367e40d6d76dbfd3998215bd921fec0cc6bc3", "impliedFormat": 1}, {"version": "32f29b2a74dddd271b5c3354efb66122ffa98c5e9e6064e8e928313ccf151492", "impliedFormat": 1}, {"version": "e0752a0fd52a56804b27e519373bb8d1de33ce3316ddb0104fbed1b2786d4f0a", "impliedFormat": 1}, {"version": "46f640a5efe8e5d464ced887797e7855c60581c27575971493998f253931b9a3", "impliedFormat": 1}, {"version": "cdf62cebf884c6fde74f733d7993b7e255e513d6bc1d0e76c5c745ac8df98453", "impliedFormat": 1}, {"version": "e6dd8526d318cce4cb3e83bef3cb4bf3aa08186ddc984c4663cf7dee221d430e", "impliedFormat": 1}, {"version": "bc79e5e54981d32d02e32014b0279f1577055b2ebee12f4d2dc6451efd823a19", "impliedFormat": 1}, {"version": "ce9f76eceb4f35c5ecd9bf7a1a22774c8b4962c2c52e5d56a8d3581a07b392f9", "impliedFormat": 1}, {"version": "7d390f34038ca66aef27575cffb5a25a1034df470a8f7789a9079397a359bf8b", "impliedFormat": 1}, {"version": "18084f07f6e85e59ce11b7118163dff2e452694fffb167d9973617699405fbd1", "impliedFormat": 1}, {"version": "35c5b1a942c6573f95cee37bd78f5b77774ec2091fd15969801587c758ddf30e", "impliedFormat": 1}, {"version": "f179b0bb3833ddbf7e8fb01bac23c8b6951db464210744feaa53e80873f65f88", "impliedFormat": 1}, {"version": "7664240676d1e8d85394fa4f59ead2275d96e8c53f011c02a95072ff3f74e572", "impliedFormat": 1}, {"version": "0d4ba4ad7632e46bab669c1261452a1b35b58c3b1f6a64fb456440488f9008cf", "impliedFormat": 1}, {"version": "221e174f5ce9840f45684b88602ada93a9bde18389bf47f7345b992561b17573", "impliedFormat": 1}, {"version": "2efc9ad74a84d3af0e00c12769a1032b2c349430d49aadebdf710f57857c9647", "impliedFormat": 1}, {"version": "5d92c77336bc65e1542c0954f462bc2c7257479b998b0def102782b49705a224", "impliedFormat": 1}, {"version": "9592a2d43de17204ee66f54e0f9442485910d45cbf26c76f9bb3d6ac0d44b10e", "impliedFormat": 1}, {"version": "6362fcd24c5b52eb88e9cf33876abd9b066d520fc9d4c24173e58dcddcfe12d5", "impliedFormat": 1}, {"version": "5545adaef38b42d016f1a04e1de1b3f5e9bb23988ab5cf432cab0fa69a613811", "impliedFormat": 1}, {"version": "615bf0ac5606a0e79312d70d4b978ac4a39b3add886b555b1b1a35472327034e", "impliedFormat": 1}, {"version": "faf43114b6264ee1b0ec2031a90784858bcc50052e243ca2b6e53ae2ffaf851a", "impliedFormat": 1}, {"version": "e9bc569086ab7f94e6a91f81852b03f071e862bf394a6d7114b19345b25c3900", "impliedFormat": 1}, {"version": "5cc020e033f6213c11c138773a6ef88e90683bea4b524a172c450c25fc6b838e", "impliedFormat": 1}, {"version": "9c448ad5d8b84a6dd22633fd6a09a578a3931002698daa04e7ec5ad81cdcfe76", "impliedFormat": 1}, {"version": "7ffb4e58ca1b9ed5f26bed3dc0287c4abd7a2ba301ca55e2546d01a7f7f73de7", "impliedFormat": 1}, {"version": "65a6307cc74644b8813e553b468ea7cc7a1e5c4b241db255098b35f308bfc4b5", "impliedFormat": 1}, {"version": "0fbe1a754e3da007cc2726f61bc8f89b34b466fe205b20c1e316eb240bebe9e8", "impliedFormat": 1}, {"version": "aa2f3c289c7a3403633e411985025b79af473c0bf0fdd980b9712bd6a1705d59", "impliedFormat": 1}, {"version": "e140d9fa025dadc4b098c54278271a032d170d09f85f16f372e4879765277af8", "impliedFormat": 1}, {"version": "70d9e5189fd4dabc81b82cf7691d80e0abf55df5030cc7f12d57df62c72b5076", "impliedFormat": 1}, {"version": "a96be3ed573c2a6d4c7d4e7540f1738a6e90c92f05f684f5ee2533929dd8c6b2", "impliedFormat": 1}, {"version": "4fb7e15507532975161e9c31452a89072c3ec462e6eeaed82e87e29efbed3163", "impliedFormat": 1}, {"version": "79dadaedc7b41f2cd0b84091d64663f3838adc0f8e8335867c801ac2741a8009", "impliedFormat": 1}, {"version": "f832da191236c3995473814d9758d25b75280a5abc498448755d0061d89b19f6", "signature": "61c2c12f2354aec0bfda4f3ae6e498f9dfa5198b159d44785c34292745c2c142"}, {"version": "9bd006ca2c22c9e19f2e596e817c9ecd3ba0bae084a62bbc2af93ae7acc72053", "signature": "80635a2833324e96f0aba376205e527592e6f04015a6a803702e72db50641445"}, {"version": "fb893a0dfc3c9fb0f9ca93d0648694dd95f33cbad2c0f2c629f842981dfd4e2e", "impliedFormat": 1}, {"version": "818e7c86776c67f49dbd781d445e13297b59aa7262e54b065b1332d7dcc6f59a", "impliedFormat": 1}, {"version": "3e8102f82e2cebc0883da04290bda483550a5fd0964a750c35e05706606579cd", "impliedFormat": 1}, {"version": "0df0cd48500ea997175bb5c9e0747dcca943f6a2b15adc06b875b3623dd83189", "signature": "ee93ac9e2f1605b684d48c481494346f8faf3bbf21d31dd27aac8c0e38662830"}, {"version": "1b7fd25bd51dee35342eb1fe86389bf19697fad0f5720497ceb4844fa5b3a1ec", "signature": "3b895e52ad0df0add84f9e6a946c5b02785ea96d54060520186e4f819411fcd5"}, {"version": "838214c44008a5b60d22d954ba7b4e52d0a6e11958e5b476b2950cc9505325d9", "signature": "e893ceaf176746b992f10832eaaf39abcab5ff1718d955f18f86851e8f212b2e"}, {"version": "23f534aa34e8aea1a83eede93ff01203b1ac39029176260dd9d80e17853135a7", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "dbfc7ec6102abc55ba0a2a5661589f0e6bb8cd9ef594da331ca951505db4cd88", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "4622c579f50c383f153ff08d11b89bb6a31c34050f14528784124a052a5b9204", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "fb499168722393a2e8223c295050f5111f5d28735497b46cf3f7ef340d3bef7d", "impliedFormat": 1}, {"version": "5a08c5b7b4a9e7a649c8b1e62cc35ed6fb7f10db4379aa905237cfc3a10e9e57", "impliedFormat": 1}, {"version": "1c55ee4b5d547aa4390b96df6a4d2c10753b2ee2feded87529c5b7962eef7e52", "impliedFormat": 1}, {"version": "7d18ca035d92930c751695e7a250be66c9395fdfaa340cb44733ad8c82783c75", "impliedFormat": 1}, {"version": "e9b16b70ab0ddc251e2b2fe6f6434947d740eade52f97da7422d162d262d1aca", "impliedFormat": 1}, {"version": "64f84434bc81b4a2a276829dfec06d57530148ea8139e1fb1b48f4b2e502c425", "impliedFormat": 1}, {"version": "2d2fe3f83ff4376d9ec91edab24423d20824f739dffe27216f9c84ee25802fb5", "signature": "def19771423bffaa0b1dd5be5a9d2fc095ac14c0eb13b915fe30ef0d1e5babf4"}, {"version": "0c92049ab6ab1a20b9f26e4144f6e93cf0b7b5dc7fb97d1bcc5e907644f0a16c", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "99120f6d33277206c11b71226c7bb5d385fa6301be326de0554db6f380472f36", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "5a61259aa03297ba60ec176a1ccdd3036c321d9a5a064f5877483e426c6c8064", "signature": "024887f6d208ea039a0c6f553867682bcdb336303f2098a6d3a4282fd17256c7"}, {"version": "374787392b333dfb8087b9c816571f5f6237f4f95ecab85a1f9a29ceeaacefc2", "signature": "603bfc29955e2296e29bfde79e203238a70561a46c219c307307353d2236e0f2"}, {"version": "d1ad79001e1bae9d3b3de5445bce6589f6488729eb3de980923c6d68145a9246", "signature": "024887f6d208ea039a0c6f553867682bcdb336303f2098a6d3a4282fd17256c7"}, {"version": "c1a44418b7e3f9381e55dea86cc32b26ec3d5ccf6510102716aaa55023919f38", "impliedFormat": 99}, {"version": "99218061d738118ffcaa0d4430f8d681b38801dcca1069947b9ec3edf1281036", "signature": "d5c107817bb794c34998def3a23ab2094e6f43d652237e12a83c816e8a76f7b6"}, {"version": "74671226d9dec6076bd610012292390274c59a484661f7be15648569fba2059a", "signature": "760f031f64e2ac7571cf738b9016d3d0ec543cde236a81750722906de6107988"}, {"version": "fdea52a5eaeebf04089c5fc3ef8449070a11e8ae04cf453dced1ecef0cf33628", "signature": "b6805e286ec3e72b62a95594ef80f670b498bd70af1ff5f23ec8ecc12d50fb40"}, {"version": "fd8878ff78f024cfeca6a8b482ec1c6fad385f5e2ae87b0d8cc0c87497f90a2f", "signature": "a47449cf85201808cd6599664471dad5a0144ebe1adf54d8ef277de1678f8906"}, {"version": "400ffcf027235653ac8ae31c66fbcdfc48eb613d33a8a770abe654c4d8366398", "signature": "ba2f2e5f704c91c1dfa8142b3967fbca08b53fe9b0e0f9bb781f0737e6b14fcf"}, {"version": "8237313fb4bb656ac07fe02c65f7d1b85450b58a86ec1a6c4ac8ce671cc361e7", "signature": "dc769b4846154fa6831f18c8b8f8ea394aa7cb0252f2255fd56fbf13ae415347"}, {"version": "80a2f11d4bf1666008d172ba219dcada40b8aa8cfbf9ccc760781e782638678c", "signature": "1a7c90087f0253b67fcca78db84daca5360c15ec2bdc3d56c4d09936d29bf105"}, {"version": "883ec695cd79359747e5b4ea083948cf0716207f0d9729e2636c6d06f7d21d1e", "signature": "4cd4ade56361e2267c342ca6836fe2aad4f5dfde2cc3e2793389551f4ecdacba"}, {"version": "aa5e1da5eb8dd11df2eb53e37df1caa2bec97a00f984c78b8f223c163b77f69c", "signature": "a6567e40987181099acefe1af6bd7a8cec2d35726ef74806ec97b1959cb82003"}, {"version": "93ed61db81c0fee9904a7d1e7a6678b61ee822804d662c95682dd2b130e63c4d", "signature": "ee4b418041a0ab0bc8869c57fd1e73b857acf6eef8bb3ef96c86f7ec3b7e26c8"}, {"version": "2a3f13221357a395f1f8f4eff7ee212e43158642df20d05dd18a0ff0df612bb2", "signature": "a47449cf85201808cd6599664471dad5a0144ebe1adf54d8ef277de1678f8906"}, {"version": "0e5540ca0a3afd1e765f4882bc6f9995e255dc1424d43ef447567835b1a51828", "signature": "f639dbc5dbfb75f9f82740d5431677a85a3d93b0b936756bfa385f5197df34a9"}, {"version": "190e77a6cddfa5545e21b463d53abea8999699a0fcab1c543f0e3b2dd04fe36f", "signature": "e1b1571e0afde2c5e5edf8cc98a5d52a74231ee1af48ae735dbd726da2e3a368"}, {"version": "82b1fba38be1e2b1036e85761c0bed66df9467fbc5a3daa8bacb006d23681545", "signature": "bacf1b5d3c9d45dd3ebd59c4e41423cb9e80d1d8e81f05f3df46f5ab132232e1"}, {"version": "275d84270bf7db44c97ca2859918175d662a405bda4e2bfa22a2138cf71cc175", "signature": "697409fc78b756e6c5491f9ea88f410dd181b762809e15a111622db9f2dd616d"}, {"version": "df12650f38faa7dcdd065e0033b8c1481a796c6b7cfebc230a7386a8c9f24187", "signature": "1a7c90087f0253b67fcca78db84daca5360c15ec2bdc3d56c4d09936d29bf105"}, {"version": "d46b72827479751585bd5a01cedb4f9e13a1c5fc31d6e3d91fbc9242b98367e0", "signature": "b597a4cc59a9545b10c06c95d80cbe11f84bd3aaf2feb6567637a9f00cf05044"}, {"version": "96327e5dd547a7bc1e42a1bdcada193a0e82a119425a66fa43b056596853e278", "signature": "48bc9d19b78bbf4447789b8a0500475ab8b1f6a289aaf71489df503e323da153"}, {"version": "1b893e9d2fc594f8f37d5cdb46bc8a65f159198f2108bb4faa1975eefdef3505", "signature": "1e6c0643f33a00eeffbb5c98ad55d0f05941e67baef86d29237ea4b313478276"}, {"version": "f0a911ee282a5cb2e97b1052cb96de034f15207e06af5deb1058222d36df10ac", "signature": "15a4d8253d36cfe0fcb27f1d33e465bb0df8e1a92bea310ce64779af3cefc004"}, {"version": "705a86fea8369aa3f0b2bbcfbd05e3a7d68ec7db57063c70d836a37e82bdae63", "signature": "1d5f60618ebd94a9a04aababf3f774879e5ff6ad4245bb21725e05ef6036dca2"}, {"version": "7d5d6e33f3d5559488b23dcebe1407b72aa32dbef6c8cd66678021fcd6212f15", "signature": "697409fc78b756e6c5491f9ea88f410dd181b762809e15a111622db9f2dd616d"}, {"version": "d33e4cb1803b9ebc3b1849473b9f3e9ed2d214b53156095dc350828a3a84ec1b", "signature": "1a7c90087f0253b67fcca78db84daca5360c15ec2bdc3d56c4d09936d29bf105"}, {"version": "59bef0b80c702d1271985fceb87a37bd061732f4f33cb5cb81c41965eef039cb", "signature": "61c2c12f2354aec0bfda4f3ae6e498f9dfa5198b159d44785c34292745c2c142"}, {"version": "42d3103ee77e1c842055296439da5ffee80ae2e9ce6e667dc23ce981bd273ede", "signature": "1a7c90087f0253b67fcca78db84daca5360c15ec2bdc3d56c4d09936d29bf105"}, {"version": "c8dd45c1dbc5c055d5740b181893588e099d8f58b9dd38126ba4028ef9a839d3", "signature": "687737b6f8129abf854957eac69f67e1270b3e7b2962e6cd042549a15819cce9"}, {"version": "68e05fc7773c9c441b5da3fad4ae698a7a4c5ab8d340ae36d0fcc1bfae49d5c7", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "e7ce01c228f5ce3c8fb2ed11dbba538a29c7e9a58378d48fc53ff573b188157a", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "c32341dcdbcb4bbd3e66df1e8fb9fc21cb093ce4d37b33ec044cb9ba92fa031b", "signature": "a804f2b4d9531902f5550b9c92398d1628e19692fc8ae6454393187afc9bf592"}, {"version": "893f32e1f7bccebcde68b2ef6a58c38698049a5405864a0b012f276566b7dc0d", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "759a3b5ca457b18330790ff09ba1b54ee3da731bacbdd665db5f44cd50674a93", "impliedFormat": 1}, {"version": "53e16858dd79f4fe093ceb32b8cacf03d16c8fe4e9ecf2a8bf484e10b8fed299", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "c8485cb8cc72dad9761a5b489e7d9663a197d192568184b445e4bc4c86fcf19b", "signature": "f57949da8fc1d28edc78a3783817fa1cee23d237f384199b49bc8afb65bbdfb5"}, {"version": "1ff483f0b9ab8b5ecdd939118926cb65f8eec64d4570a34f19cd5bdd0098190f", "signature": "13eb7532d2a1ee427f093b2516406326bf014ebaa69099ce9e77bfdd045ccc52"}, {"version": "2deba01ab55b687cca63ffe1adc8a669d6958127bf82852de889651aca01a72b", "signature": "dbc163e39402be34dec4695b74b93519c8158bcf7a1d45aefd69acb37b1add3d"}, {"version": "0650b8862e73d739fa666fd83424245c44fd8b4b00684092a743c65209f650cc", "signature": "9d81845c55bfe7011ade4a856eebc958c25c97833d08123d0b35b314c418882c"}, {"version": "d370afd43e276f19f7dc3c5fe2b72d07851160d2a8aef4ee70d730f3a3454b0a", "signature": "08cd81701706765a05dd9dfa9bfffb59456603f438fbb2d4f0ae7e2178b241cd"}, {"version": "8b676b3e9a91b0e4aa38b95f7fd8c44208b977fdc5c778e5d3c5bf0329e1bdec", "signature": "2acfc6372ea2af616edc1a0eab5d6f13d42fa4c264c3bc65cd47fd6877b25759"}, {"version": "3a54fddbf87a13ad12aae3d05fe5687251ba2cfcb775790f6b1e1d1368f5307d", "signature": "156ff602fc4e16529e8e034bc4c2349236216fc2e708f671e59cc0e3d530cf1b"}, {"version": "b4daadd47a0676659ac0fdd700027d0865979e9b67b1e7aca9c7c0585e8fa841", "signature": "697409fc78b756e6c5491f9ea88f410dd181b762809e15a111622db9f2dd616d"}, {"version": "427890d2a045616a7cad4654f0fc467d0d6209a8e7750513d87e645f2ebc183d", "signature": "1a7c90087f0253b67fcca78db84daca5360c15ec2bdc3d56c4d09936d29bf105"}, {"version": "2e3f3d672326b2d93a8406e8df45a8a4b81796a12ed576beb62bcdb0b1f62a52", "signature": "e1b1571e0afde2c5e5edf8cc98a5d52a74231ee1af48ae735dbd726da2e3a368"}, {"version": "87b1e3fee24b4c134958d50610ed0251907f3bb1b856344d63a5ca08fdf53f88", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "127e60d90222773d46a413c26e1e97d417a2457022feb3dc11fd0cd95c9996a3", "signature": "72129474532c5dcde81ab2816aaae1585a242be5add5ab0cd889b9e3795832ee"}, {"version": "129d1021bff73ac9b7c400388d5b31e5b3a5bf2f374ca76ceab9eaddca71a49a", "signature": "e9be2ab43599e9a50ae58ea45f1f843d1f622796ff1e99aa072f60e382480ac0"}, {"version": "6b43ace124d03b6d49d4e0d535151b7527cfeae0b92e25b1a3e09c2297b2abb3", "signature": "58147b446ef420cf0e87978d46b5b4cc33609b647da842d75b997a2ff82e01ce"}, {"version": "42d3a61134d1759b0b419e83f387c45095d3a8f18a2f495547b068e3884ebca9", "signature": "e9be2ab43599e9a50ae58ea45f1f843d1f622796ff1e99aa072f60e382480ac0"}, {"version": "7e8581061a735562761df2f0f1c9d80f8c2d1181a3c92278ff25d30a46f126c1", "signature": "72129474532c5dcde81ab2816aaae1585a242be5add5ab0cd889b9e3795832ee"}, {"version": "1eee4514348d993ce55c9773fa1a59fd99cabbf95d4d55b6267f32421dfe2530", "signature": "7bd67567b5960c8e74c70ccc6c95f2e74ecdc8189d75a089d3ecdbbcae0882c3"}, {"version": "0779d0dd79df4e0d9eb6123776bd95a2827bebc37b6f5e65dba28843270cc9ed", "signature": "2b960d1cc166d842386a4bb1bdb7ff295fec5e4d9fac03b317a348348dc03021"}, {"version": "bfd03024dba662d621391bd0f4c785e1faa419ce4858d48a7dfaf4b0008b92bd", "signature": "29d535cbb91bf995b39df1379db90a7314103d9e17c15442262c614052baad91"}, {"version": "23faceda340634b3a54ffe54a2637c5be43b387cef8ed2951791740fc7eb7963", "signature": "760c7853bb4efe373da8bc90be6014580740b6dddf499b6a3cbc72e41260bb40"}, {"version": "92a6f65353487f14ef104061e4f4682b8e933e479529f5d6fadb23100697c37f", "signature": "a2090a09c305ea2194374ec64c660cc0f3dba637660162d204cc5dee20400731"}, {"version": "78e8cffd7eccb11ed1476c1872538a64a01485a4e4ec7c6d092a2b3739ee33aa", "signature": "b58f058aaa4dd9ca592df7071e67619fe935f250ef6eacd0a497d6b2806a9b33"}, {"version": "ffbc9578e1ae84a9ffbbbd12236868d47e02476acc8a408c887d5bda0dbf9da2", "signature": "600101bdda452612abb72d859058f03180f39b774209bead9a09737a073b20da"}, {"version": "b1baf511d74b5502eaab2c5da90bc1ced255f9b5f34582c40b36a4511eec48b5", "signature": "53d4f1f7aef4e5cd55a50f3d61668abfdbd7ffb213195c98f01b89b2e7967012"}, {"version": "6bc7003795870a420d204eb2feea5fe7711d81db6495047889a73380970eb662", "signature": "ca74d5f6091666e4e8791a10e6aca9138efcbc06c8a4fb62e1f66aff7e0162ff"}, {"version": "70091216f4fd16d9b4fc1578e7b514ef3416d8856f79aca743b4ad23119c7df8", "signature": "94cdb4bb2298aeb11cb0a53e8cb01c9887976daa4e168870940b01b066e62344"}, {"version": "cbd06e3b29333e34e87458c89f00f6a3fcc4c05aae20a93510994055709dbed4", "signature": "5f4ce21c48b8ab0c1a2d33fb016e6455b66e428433ceebbf68fb926dc40f2807"}, {"version": "c104b2ad320bcd2b860f494a378c2cd7d8d224aa43544182ed09d8082c739b29", "signature": "30e995a0f3afe80b76f6fadd56bcbee27595f9512883caf3839fbb0397ea04fb"}, {"version": "793c9a3937d43e9a84b3ccf9c5c97330cc21da4f9d9cfa0fa7b8f5780366c3e7", "signature": "ae091294fc1a50cefd61396e7bac09357eaee29e02d8a1a6a70affe52ca95233"}, {"version": "f02064a571a82a897efe6e24fa1ee96b0f71cefac1d916203cb6aea5a2894c20", "signature": "983b4febfc66d80d2a38f1a335748da135ceaa187315a489f342774fafe58bf4"}, {"version": "6b5627b0035296c43a31df6888105e045195d1978a3f8fe4f6bd7bc698c07a43", "signature": "be759a9e3299128a3880112fc3ebd15154b5901b995134c3426df33ad6a3a467"}, {"version": "0106c8ee9f89486573560e9ad94ab4c4218f2b6155319c03ebe2cee6fb2fd146", "signature": "3330be2f262c7b1057d4deb55a4e65b7c31a5614285d7204d068e427fdbdcdc4"}, {"version": "7ccf40557af29308f875f61ba8ec7459d72f22d1b4258426fad4980f6998a96c", "signature": "2ddb290203c4c3771241cc899db02fa21b9a9fd6374570a395da2fd0b39b022d"}, {"version": "9d05aafde114da261dbec6190cd50440bd4d11bdc4d280fc1282e8b58e39b47a", "signature": "8b3ea013dc1c2ca4b835f054f61336e338c4a297d64fd99929b55a63f6c4aaca"}, {"version": "4db8dae8e46128e74409e6ef37293965a2d38f6a96d80c67f507e4defeba47b6", "signature": "57726652c0c39b67b55af8d46c1af11b46f8223625134366df94b2a8b71217e3"}, {"version": "392557cb468c1e0348cc2479e79770e53cfb9e4c06df8fed8e5f3a58df75a6dd", "signature": "ec1ae456c114f6af970d49109b0ea63c458c6618ab6958cfbbfacee3e5e7e0c9"}, {"version": "82075e4c510cf1737b1148f6a18546f289ba35a4a81dae29f010f9b086dc2596", "signature": "5c0a53b65a37ed39a45e2dd3db5a521ca5ad9c4822084f7005c89c5f2877312f"}, {"version": "d9dc700d1740b13689035520adbc13fc7e8480702b52c863b3bd131465a23a07", "signature": "ee3a94293737c4dd7f42a540b4804ca230776d8fc457d96250792c43b1182751"}, {"version": "d1a84d4a3e80d81f48d827a9c8443dd5cff62b40ac9d8beb73976e0c167830fe", "signature": "3f0cd40d9145f638aaebe028225bf9e1c7ff8563ab5fd589211595dcaaeb5373"}, {"version": "86935c4cf3beafae824af6fde152eb0c7a465b72438556d58795d9e803dbfac0", "signature": "cefd532d5e76555049f9834e0f6ef6040f3e44969ce03fd529e87a7e495fc5e3"}, {"version": "185fe37f187d30c44f62b535c09f0f42154ca55793c2374a6a7949a9b46f2bcb", "signature": "876f3919b8619f5865d472e8a195af246e446185e992c213e5ecd47a482da479"}, {"version": "29fbaac7963d46a08886725856f2f0d1462e56a7cc76834b20cd4119ba5f1489", "signature": "634702b08b44bfc08a68d3783223ee3c610189efed3282d36ed5f4ba0c4859dd"}, {"version": "6e099c0f85a50903581fad6b0511b3fe9f6506fd802f34e6c911b4e3fe552e0c", "signature": "d680815d1f072d4f0290b02773946d5b4b6e7804d82fa72adb1e0709da03802e"}, {"version": "426d16daf5a6957d65cd54321fee3e86fb2fed9f50fab5d4d508e56134715150", "signature": "af8c76189f4c9fe205afdf1570d7911198486b0cd25b8ce4c3aeec0946900873"}, {"version": "07a9bd2788a0e53c2b306f4c22ee1ddc2e87816a668f3783ee343f0e2450b935", "signature": "606d9d903b092868cc6323b3d7a34d21dec62ccc1af32d6675832e4c31ac3bef"}, {"version": "9055c0a9ec1352a9e7d814e58be76a65f650252a987b331edeacb54a85f23613", "signature": "7b6d404486792b81b9a192f0aa77255dd37640b146ca2a560d0b39586fec96e0"}, {"version": "b3101b33252ac467129a604772de0c91b74374875f97805914ec86d4549654b4", "signature": "b56cc19039d68a13083c38aad9c8852abc324622e2e4cf11413050a854a433ce"}, {"version": "a3c372a77076d463a2d5732148470c0439f81a2d17751e861b9b991af4636203", "signature": "f388fe080db6863ba3f49c8840954d23d36253a04cd1e8a22bac162d16ac8296"}, {"version": "430f5b5f3d16a6c5057398104f878290d6d4f797bffa77bc19bcd3f7c98b1281", "signature": "0597da084d532cff4f6aaec60ddd075ceb668c13934217b502858c36daa67242"}, {"version": "6f88a85cced335bb116667d1ff10c7e21b7ec1d1d811f8bafde3532a52f71e1c", "signature": "a9cdc193c6d7586454af434fc6cfe76df0f6181f9fbbe3de1df47ed5984523b6"}, {"version": "0a2c27c7fe82ad1d559739d186904db9d1ee1f40df58016c207694cfcf5d53e5", "signature": "2c72ce2961ae881d92e0aa9fce3624d4a9c808024a29b47c4250837a63637982"}, {"version": "12e2df1445769f036344ae682731515af0d2ecb1c307bbccb9fa316cb4372961", "signature": "67613aaa4ecb4eb760d8ed104e1a081d680a8f6c2326823d2b441996719b29d1"}, {"version": "e461de2ba66a2e7f0ec1765bca40242ec2d5a596e54f94d71e354c66c1d4d4f4", "signature": "f632082893f0888fcba47b640c74b2de058ad5b959b1e50a0aafcd5bac293821"}, {"version": "6783506b7aa9a1f7620b6b1a67a6b9b6cf58451d971a878f99664ef7ad04a95a", "signature": "06358abb85a8e844b68eb0d2b7feb6b0a125a98ccd540b062bae3defcda07665"}, {"version": "0d29a66df10f436f2e451d9822a3080ce3d94bc4d3f08126e95df35042c1504f", "signature": "080b441327dca8b601529b41d619352f2f6cc2670bbf84de71b74b4ab38d44f3"}, {"version": "d4c26856d13fef2a96c66011fa660f2316df9f62f0d3e859f33bd58c92a14585", "signature": "5b17d5a5c27a30b70bb213c4555df99554f982fbd6d07537ae4183f9bcc0b86a"}, {"version": "1c10dc19f586020d01426f38221d7a071628b2ef37fadcbd91540e2c07b9cc39", "signature": "ba0c13199a6c233154e79cb908a7a19559083ccf5e746cae62a3f42be4217d8a"}, {"version": "2276ea8fde989ed585110ced65b4502c6f4c9befa2b60dd5a675dea9ddd82f84", "signature": "41b662b46f518ddf582ca559b0a82799ee919d03fb6888361e35bbba963666b0"}, {"version": "84c9fd29fa04780a36a25207773ec6248162f471e98dd35fc63f4ea56725c9f1", "signature": "fad665f2c8e1e53d556ba0cf8529841227b84a8a6f37b9467876279bae667e86"}, {"version": "d0f2a06be501e6c983a9f78fff6b5ec7c17794a9a2a44db0aeb384b7f7aa9293", "signature": "94cf50ca3515f4fd61e1a9dc42c69afd8dfff39890e24db2118717121597af44"}, {"version": "4fd8718fbde90883b9c753fb0ea9fe83ed4d2ccd586868fb85c2f3c06454e68d", "signature": "f9b0a647c25840eaed35acd51974aebd0215d7750175f2180e85cd49d637863b"}, {"version": "ccb652e9cea5a9daf6c5141bb6fe5ce0175b4fbbe1eb2211fbcb91c4531b0baf", "signature": "826a3211d65c56e7d955da4cc1a2ec2563149989d8862d29a97cd22274795c87"}, {"version": "58810fb4705cef7418a97a4797b4844a8914ebfebbb51fb55a4de042fbf89351", "signature": "28847d2c33286f6eefbaa23b4b63076cdd3d1629b911fb50e70cc4a6ca7a4960"}, {"version": "00f827ac3bd8ee909825e7586864e5b769b9ce48cbe46a79ff2f7c0016879153", "signature": "83757ece12c4e3149373a19b7d8fa57ffcea10b983f04f956b7121c79c143b61", "affectsGlobalScope": true}, {"version": "52b978789776fbf6b8505209dec9a901d36042ff5607e3ff9299c5e8821ca5e3", "signature": "b8df9cf04989e0bab550ca63dc8b032d02c27d6e85229442f6d3697acbc11538"}, {"version": "f94419271795c4efb768541ea0e6501eb6c41f0675c54b70520cfe88cf48de97", "signature": "64ec28a285c4e42afea36b5685d24da615615d24bd40aa039926141aeacdd6f8"}, {"version": "9a5fe5f3a6ca5080ee9311bfa49e43d447162db06093edb26087181b10fce944", "signature": "a8e55f31982803a5b7c61038733563e032269cbfcbf8eb12eb458d6ca7167cab"}, {"version": "4e3cb97c3ceafdcef56a4d65c91d83b8b58154bbc8395b8fde7b93a8e56ca659", "signature": "4935ef12eec7d0246e048c90e90f03a1c077c70aa57654166cca47f201e66d12"}, {"version": "3d232c754d482cd172d0d27cb26cddb940b0bf21537a34888184cb02f3f2a33e", "signature": "8bc7bbc5cf310ea58114547b8ba5bc116687940eacfe6646437fef862227c8cb"}, {"version": "75ff6d69dba726a9857eca85b9e45b78e01ef7fe8ee93c0596736d8b39aeb51e", "signature": "87a86fbb05ec3f5e1bc536e8ec54b4f097402a2592e21ca062a834b732d5666d"}, {"version": "98627eb578134fa8004158963fda03dec83c49b97871738fb71df8003f706589", "signature": "7e87055178fbcf60006e4264c54d5db74d29e5f5004330c7a192824f71b8f08e"}, {"version": "10009b06471e5db88c80cec19b02e6c4db18fe59d8abda5f1c7add5d30c6a16e", "signature": "9d7b0a2a0e4f65d924a218950310f19ffcaeedcea9e0be4c9692e06b105f7643"}, {"version": "ef3efb15f81bc7ab0a26583b8931a5bb80e072f87e18a80c3120187dbca4e45c", "signature": "879f8f917a7bf36bb925033857fcecb89e98bb933870439f709d1531ca778516"}, {"version": "abaeea308a34a4702a6758af013b28d3570a215782e1fe61a706b0676f066e80", "signature": "a5f9812a013f31dee766bb86feb0ba60fbdf9c60929b34af6e838a4ad380633c"}, {"version": "2f2d3e3f2dbe973fba2ce0af102cfda7d3fe1e7ffe084b622e8d83a5656037d2", "signature": "c8fab490b8a42a53aa01594322eaf34deac8bd23be2083897929bd0254e3ba65"}, {"version": "70d96aec95a1dd1ec55a20428186d7112bdf18ee5484b191533852aa5b228962", "impliedFormat": 1}, {"version": "f7ace0cc53ba990b20c1f198218628b71ba07819ebee606bd3a920474587b718", "signature": "2d44327b3f533ef1737a6cbc4ba25a2879b50681abfb0da81f11da3b7435f46c"}, {"version": "0bf39ac9eae0cd32a07f6dcb872955c4249f887f806dd7b325641ce87a176e42", "impliedFormat": 1}, {"version": "885ae130b893e5f8f56d834a05647a47eb063ff268a4bb05cd48fa09fe162f67", "signature": "443eec2a39f0fc7d42b0787e92d131fcdd74162fd6406be0caebcae334745d42"}, {"version": "4b0914417b60edcb9274ac4ad335956a874c07ffae765aa1fcd63d3e4ef94207", "signature": "b9763ab53973201db050ce823d32f2e73b89a51f8cf9590ef7b80c7db9db7ee2"}, {"version": "322b8c55c60b07e2e07b986921129b044dbc20004b482f4e005a9d5f16d11607", "signature": "f33a34d389bc911245ae6d2b5f6f864e8b7867ad0c34f54499185d80afb46b9e"}, {"version": "12776b09c670bd5c2f76add0c392ab7145b2543678fc12e11ce6a0ce38610344", "signature": "dfc3a060016201b5745e8d136e805da50beb8b27f6e43aecefec53902e3effd0"}, {"version": "f267e77866a8ee043402efb01d7a5a3263c4af7023b82ae8c1e86f3a16ea8f5c", "signature": "d27a9356be69b5c023f5284d747415f8716cfea95d3c7da1736c2a8312327027"}, {"version": "1bdec19f41afcaf3e8f190870d0703fce9834242bf929a33081be8f2b36fba65", "signature": "37a96bd81e5470338b65f75b5bd310e565e021a38205891d3f084e062e792013"}, {"version": "1c0fd77a8e54aa0591b151b543e91e23cfd66d6adee912019e907043067f0c28", "signature": "f12d29d2675adc05f044751fbfe31805ed16fc71d9fb82ac2459cf93b34d461a"}, {"version": "0af0fe8b61bd8dba8d94be8d0916e65bd9fd8581e77d15498a1d154e26492de4", "signature": "4c4f5c28939f807261ba3778ad9a603fe833ca8ef57d2b17a550c7060b2e1882"}, {"version": "65723b50b2712b672eedfbb65c8c5319554ccc024ebdee58067c3e014a6a52cf", "signature": "bde5392919dff156d8a8d647dcc781d2411f5ab29a523235fa72fb4e0f199a6b"}, {"version": "fe93c474ab38ac02e30e3af073412b4f92b740152cf3a751fdaee8cbea982341", "impliedFormat": 1}, {"version": "db1bd09bac9428d99ba02dee9a795a7deef983da64502984cb89d5175ba041df", "impliedFormat": 1}, {"version": "1e00b8bf9e3766c958218cd6144ffe08418286f89ff44ba5a2cc830c03dd22c7", "impliedFormat": 1}, {"version": "3cf222dddd8ba39a990ade050fc153311d324d4494cedf59b25c281ebf38dfbf", "signature": "8b6573fd075c2aa5e06ce1446f00e27bd72e4cfe64f0ffba1385a9a39c1c8c2c"}, {"version": "0f1c144e3654406b193d2a16a96c99c329a5f398c142975a35a673c73938766b", "signature": "e6a69828a2c04fe4bf4180e25be2aba7316f508b3597703557008d68b8d00182"}, {"version": "510d4cb0f31d2610676714e036f85542d5b0a9e3bccf52aabf26f30bd890eb31", "signature": "546df520e1dfabd3b1f8d1ae6e6d22c1c34199fe7cc23da581f68ee187d2d06f"}, {"version": "228cc267fbb4ccdc8b4b6bc9a4e8539d07e0725c508048e9916853caacdfd767", "signature": "6c45675928d5db83b67f6f8d0c2238049d5290a975c3d1c99b7d2cdf8b415168"}, {"version": "b12aab90f9a0b39d868668d5ff19398688dc2ead5c03ca8776707478fdd18c82", "signature": "cd368ed14033467d591e6a99fc4f86ac7f7f6f7d4e0466ca591478335e71fce0"}, {"version": "b66d9320fbe7113a95f5eaa6cae4577b8b64a308ce94e103d324d7c2b2621fed", "signature": "f2646bf914e3f6f05302a4b6a5fed534b7ca7884b687ec5df3979434859f195a"}, {"version": "7ef96d4ff2514b0632a81725039c0982b380b2c7a6a1e7097a458c5d8232fe53", "signature": "1c2142391a187bd44b13564b152f40b9d43df3bd696f87113c5b7a0566b0ce0b"}, {"version": "c8e857b6224783e90301f09988fb3c237fe24f4ebf04778d0cbe8147a26fffe7", "impliedFormat": 1}, {"version": "df33f22efcbdd885a1ea377b014e0c1dfbe2e42d184d85b26ea38db8ee7834c4", "impliedFormat": 1}, {"version": "f400febd2140549f95c47b2b9a45841c495dfeb51cc1639950fa307cd06a7213", "impliedFormat": 1}, {"version": "7048016c91c6203433420b9e16db56eec9c3f5d5a1301398e9907ac1fed63b58", "impliedFormat": 1}, {"version": "a4234645829a455706bf2d7b85642ee3c96bfe1cfddc9918e25bac9ce2062465", "impliedFormat": 1}, {"version": "9ff2d17592dec933b2b9e423fab8b8bc20feed486f16d35c75edd77c061de6e3", "impliedFormat": 1}, {"version": "fe9fc5b80b53a1982fe8fc0f14a002941b471213717536987d0cf4093a0c90a0", "impliedFormat": 1}, {"version": "4921f21de15ba1e7d1d5c83cf17466d30d4371bc9acf0c2c98015ebc646702ef", "impliedFormat": 1}, {"version": "f728f13a2965aacfb75807a27837509c2ab20a4bb7b0c9242e9b5ca2e5576d22", "impliedFormat": 1}, {"version": "c340ac804b0c549d62956f78a877dda3b150e79954be0673e1fc55f4a415f118", "impliedFormat": 1}, {"version": "2bfe95f5f0ea1a7928d7495c4f3df92cdc7b24872f50b4584e90350255181839", "impliedFormat": 1}, {"version": "9dfe677f6d3a486eebe1101b4cf6d4ec1c4f9ee24cc5b5391f27b1a519c926f7", "impliedFormat": 1}, {"version": "2766c9a60df883b515c418a938f3c8fd932241c89aba12aedf418e02a73017ce", "impliedFormat": 1}, {"version": "394967bc5f7707312a95cd7da0e5b30b736b7ab2f25817a8fea2d73b9398d102", "impliedFormat": 1}, {"version": "014a4afcc1674f40c7d77ca215e68bb3b0a254c2c925bcaa9932b6fb8f1ccd4e", "impliedFormat": 1}, {"version": "f816538db9388ac17bd354cf38d52da6c01d9a83f0589b3ff579af80cff0c8c6", "impliedFormat": 1}, {"version": "d2e0c04dce50f51b98ee32fd461dfa6e416a4b703c3d6d7e7fb7e68eca57a8de", "impliedFormat": 1}, {"version": "a8995e0a2eae0cdcd287dca4cf468ea640a270967ed32678d6fbf89e9f56d76d", "impliedFormat": 1}, {"version": "b151ad192b8e11b5ca8234d589abd2ae9c3fc229cdbe2651e9599f104fe5aa6b", "impliedFormat": 1}, {"version": "c37f352ab276b3cd4117f29e4cc70ed8ac911f3d63758ca45202a1a052fa9d00", "impliedFormat": 1}, {"version": "c97ffd10ec4e8d2ae3da391ca8a7ff71b745594588acc5d5bdef9c6da3e221bc", "impliedFormat": 1}, {"version": "74c373c562b48a0bde3ee68ac563403883b81cabe15c5ada4642a559cbd5d04e", "impliedFormat": 1}, {"version": "d42fe36f52e0ae09274753ed0fdedb32c42c2ad6ad247c81e6bd9982d1762004", "impliedFormat": 1}, {"version": "87f162804c7a5615d3ea9bdb2c828cd1d1f8378d5e2a9c3be1bd45c12f1fc1a5", "impliedFormat": 1}, {"version": "ccb92f285e2f3a3462262945fa59506aebe6ec569e9fec223d45d41c7c6cd447", "impliedFormat": 1}, {"version": "04e45000cf1381e6a84196aa01ca811ab192ca0a09debacc9e75dcfc6777bae1", "impliedFormat": 1}, {"version": "566007f48fa4cc7d29e4cb5cce9c315ccd52b72300d2d45ab0c639889e42d455", "impliedFormat": 1}, {"version": "4c2f8fb8a8f4afce6e05b9c554c012eb50147084933d78f7d218108740afd803", "impliedFormat": 1}, {"version": "6f72b3ebad0276cfcc7291fd2aefd1fbbd229487ec1acbbad03e798e8760e02e", "impliedFormat": 1}, {"version": "096681898d7131c1183f164ccfec478d99a9efa3744a1b6617116bc6713ed7be", "impliedFormat": 1}, {"version": "2c9626288e967ebb03ec2bc27ea504f6f829b1686f65b86fd5074d53e0160d70", "impliedFormat": 1}, {"version": "4de35fb3800a92324c59c1d2ed28a4dc1284d507d27ef2eed680c2f9ebb32cd2", "impliedFormat": 1}, {"version": "4c3cccf01f76ca4292746b6dfebd6df4382eb7a05315724116feacecf952f106", "impliedFormat": 1}, {"version": "492d1d21f79a8fa084e9dfd8fab89247301a49f1a0c12765b99c30a0ad8629ff", "impliedFormat": 1}, {"version": "69872cabf40dd4399939184cd7c5e47da62a9df811d3f56d193a437817a85b21", "impliedFormat": 1}, {"version": "19d00382e69115eeb1214d9b865030b61ec14f1bd5e91fb6e2b75acf5a6bef80", "impliedFormat": 1}, {"version": "3c3045d2661ef44458559f6bd48ebb47ccdfcbc513d859dc60c5e18e0544ac87", "impliedFormat": 1}, {"version": "e1de43a7fb0dda59dd9ed398fa306abdcb99da16b54edd3c7dc5e1a45d7e91df", "impliedFormat": 1}, {"version": "8301449ecbf03d5f893c298863fb66d97f1becb31f157276bdba7c708174a5be", "impliedFormat": 1}, {"version": "a556bdee2de2416a026022aeb260b5d684da34e322b5a95c7503143e51787b4f", "impliedFormat": 1}, {"version": "e8bc04f55c1b3da172412955b2785de54f2e1f2c9cb8949c0748ff143525310e", "impliedFormat": 1}, {"version": "683ad3639d8a96cfc782d672c44797d13c735ca9792d6c57e2fa5ada89e18e0c", "impliedFormat": 1}, {"version": "25b171a82c55909032e85448d89f8409e045a24a2b0458080bf304845b29b6ba", "impliedFormat": 1}, {"version": "ce25751e5374e1f13100276ecf2e2e8aac4d4c7229f762b3dc206639640954b8", "impliedFormat": 1}, {"version": "2f0a5a8ef5c6f5866d3caf04151422d05e64765ee250a7e9defc62908cfe73af", "impliedFormat": 1}, {"version": "79726fbe0854724f5bc3f16d4e40c0b320bbaa7a6296d1d782d70909f3b3a2eb", "impliedFormat": 1}, {"version": "6d391889910947acbe7d110271463ef74e7f65ae372d355756b1a6b0a987168d", "impliedFormat": 1}, {"version": "b3dadc705ad865a3acd5b40561ac0dcbce38fa28872ecb903eb586bd64cfa8b6", "impliedFormat": 1}, {"version": "8181adc6c7145eb6b2596249f3a2e1ff2fa7ebc604e73fe583f98c4b40916d6a", "impliedFormat": 1}, {"version": "dc84bb520982504eb30b09b870b32be8eccff2cd9beb963efd6a78971ae104b6", "impliedFormat": 1}, {"version": "bafdca74b47f54e116a9f2d589d39f1c677c777198b96a677a2d2f628b43c8f5", "impliedFormat": 1}, {"version": "9ccc168fc7cb696b5f60f216c72881db1f6c2d8e39eadd6c68130711f8eddd19", "impliedFormat": 1}, {"version": "6187a2dae6a9d910f272bfae324625437343f43a6ff48a28a5c5dd5e9cfc2d5f", "impliedFormat": 1}, {"version": "f063f87a44b1e92948bd5ef6db5b8cadef75218126e75ff02df83196e2b43c4b", "impliedFormat": 1}, {"version": "333df4996910e46b00aa9b7c8be938f6c5c99bfbf3a306596e56af9fff485acb", "impliedFormat": 1}, {"version": "deaf2e9bfb510a40e9413d5e940f96bf5a98a144b4e09a0e512efe12bfe10e9b", "impliedFormat": 1}, {"version": "de2395fb1d7aa90b75e52395ca02441e3a5ec66aa4283fb9ced22e05c8591159", "impliedFormat": 1}, {"version": "64be79c9e846ee074b3a6fb3becdbb7ac2b0386e1e1c680e43984ec8e2c2bbb9", "impliedFormat": 1}, {"version": "9c09e723f7747efc123e19f0ced5f3e144bbc3f40a6e1644a8c23437c4e3527f", "impliedFormat": 1}, {"version": "36fc129c8e3ad288656ea0e9ba0112728c7ec9507c75c6a3bce6d66f821a31d5", "impliedFormat": 1}, {"version": "3771470dde36546305e0431b0f107e2175d94e11f09b116611156f134364127e", "impliedFormat": 1}, {"version": "18c6715ca6b4304a314ff9adb864bd9266fc73813efd33d2992a7c6a8c6e7f73", "impliedFormat": 1}, {"version": "90cde8ac2173d2008c51996e52db2113e7a277718689f59cd3507f934ced2ac2", "impliedFormat": 1}, {"version": "69d01aac664fe15d1f3135885cd9652cca6d7d3591787124ae88c6264140f4b1", "impliedFormat": 1}, {"version": "55ab3dd3c8452b12f9097653247c83d49530b7ea5fe2cb9ef887434e366aee8c", "impliedFormat": 1}, {"version": "abd2ce77050bfd6da9017f3e4d7661e11f5dc1c5323b780587829c49fcac0d26", "impliedFormat": 1}, {"version": "d9dfcbbd2f1229ce6216cb36c23d106487a66f44d72e68fd9b6cb21186b360cd", "impliedFormat": 1}, {"version": "244abd05ca8a96a813bf46ddb76c46675427dd3a13434d06d55e477021a876ef", "impliedFormat": 1}, {"version": "5298f6656d93b1e49cf9c7828306b8aefc0aa39ac56c0a1226f1d4fba50a2019", "impliedFormat": 1}, {"version": "93268ed85b0177943983c9e62986795dcb4db5226732883e43c6008a24078d7f", "impliedFormat": 1}, {"version": "843fa59ad0b6b285865b336b2cbc71cdc471e0076a43d773d580cb8ba2d7030d", "impliedFormat": 1}, {"version": "aa2d452401748a5b296bf6c362b9788418b0ab09ee35f87a89ba6b3daa929872", "impliedFormat": 1}, {"version": "a4ef3c3f6f0aadacac6b21320d0d5d77236360e755183802e307afd38f1cbcc9", "impliedFormat": 1}, {"version": "853b1daed2861381ddda861a0450ce031c280d04caec035cc7433872643871c6", "impliedFormat": 1}, {"version": "1058ed9becf0c63ba0a5f56caaafbfd0bf79edf2159c2f2f2fe39a423ae548ae", "impliedFormat": 1}, {"version": "8b6eab9a4a523909ee1c698a10d332c544aa1fb363f482fe60f79c4d59ca2662", "impliedFormat": 1}, {"version": "f2b2c244b10a8e87192b8730ed5b413623bf9ea59f2bf7322545da5ae6eae54b", "impliedFormat": 1}, {"version": "92bbeada67d476b858679032b2c7b260b65dbccc42a27d0084953767d1a8cf46", "impliedFormat": 1}, {"version": "545afad55926e207ac8bdd9b44bb68f0bbffc5314e1f3889d4a9ad020ea10445", "impliedFormat": 1}, {"version": "4c8ef63125ed4d1eef8154ec9da0b6b7ca9effdf4fa5a53ab74a9d73c9754ff5", "impliedFormat": 1}, {"version": "e76a7e0b4f2f08e2bef00eacc036515b176020ab6b0313380dd7a5bd557a17f0", "impliedFormat": 1}, {"version": "fabd983e4148e2dce2a817c8c5cdbbc9cf7540445c2126a88f4bf9c3e29562b2", "impliedFormat": 1}, {"version": "a80c5c5bab0eb6cc1b3276ac276e5b618ead5de62ec8b0e419ea5259af0a9355", "impliedFormat": 1}, {"version": "d8cf5ded7dd2d5ce6c4e77f4e72e3e1d74bb953940a93d3291fb79158e1afc6e", "impliedFormat": 1}, {"version": "bdb10c13a7ababaae91932d0957ef01cd8a789979cd0b606a2106d198848b16c", "impliedFormat": 1}, {"version": "0fd3f9fed4dd35b1b07c18b4c3f612b7542c91835ad8a26e0e83d905709543dc", "impliedFormat": 1}, {"version": "441b5f5ac4619df9dbf436ecdb9f0bbaacf8696e6fdb2f81c6f5b1db76f5a1c0", "impliedFormat": 1}, {"version": "5d2284728400ee7b4fd1acd69e48d649d4056916cc70950a0000e5d70a32a750", "impliedFormat": 1}, {"version": "27ef186120f9e7ee90686aa7ad5163eb5c7f4cdeb19bb87850c4a5fe4b8e05e8", "impliedFormat": 1}, {"version": "4f1f9e056e0c9d23031367b4c7e7eedffb3e1ed58e64befc90749ca4dd9363ee", "impliedFormat": 1}, {"version": "2b0ccf76bcf10f61612135f951a74327ea0a2d5a80f397b767e0e0b08cdac265", "impliedFormat": 1}, {"version": "4e42e643f05a7fa69581a1a697a1cf967d9b2657dd9dd66e59d90500ec053ba0", "impliedFormat": 1}, {"version": "0ea8485dc0bb7d2a258a93b16305e17fb5be9f877a9df88de7023a9821c537ab", "impliedFormat": 1}, {"version": "5c221ba5333b775cef94d4a30076cc30730cceba649e9d30c5a7224a698c8825", "impliedFormat": 1}, {"version": "cb61ba4d5b5e39ecafe74ad7d88dc8e67defcffe15fb9216addee0fa06d5df38", "impliedFormat": 1}, {"version": "d83e8f0c10477fb4a7729a51aaad853cee81e0e332581dd2244da09e5526b5ff", "impliedFormat": 1}, {"version": "c8933a5b693306696e78315dca1fa57f6f5493fed44cd90aa2d4a4d354dd6516", "impliedFormat": 1}, {"version": "af8e2bf3df20cd2e6b8d744dd83499e174609d0c88864af3f30cd43671e719f5", "impliedFormat": 1}, {"version": "4186fd8b51535399c7ad1edc08f9c4ebb2a9e8e327b131cc1f950c5dfbb0c358", "impliedFormat": 1}, {"version": "b92965f503f55830702062f9e0832fabfbded49ff28728686a6fd84aa32f454d", "impliedFormat": 1}, {"version": "172dbc7933ff46ba3b2efe8b5c7828fd4f0d45c08755df8200213b6055d57f2e", "impliedFormat": 1}, {"version": "89e2ec7ed42725f89fa537c38f20144782bec6c5710e467a46a647647c8255cf", "impliedFormat": 1}, {"version": "5165882999957fa041e423a4fb64627dcb310bf50183af70a6ee8e10a584b0c3", "impliedFormat": 1}, {"version": "390997d64e1e5721fa807aa9e05c97086f58627170d9a7ed84b127126a3e5202", "impliedFormat": 1}, {"version": "00cf8ed9b47860a5f8cc0a65d7a41f85a7026f68162057728abc9249943a8629", "impliedFormat": 1}, {"version": "fc8b086c99f6d721eae8125a96833e0ba1762d00b80aad1d55c7a8b59d007466", "impliedFormat": 1}, {"version": "ff72c74ccdc5570c4a75a93e605a5586596444d96048d52c72f322da183c556d", "impliedFormat": 1}, {"version": "b8755448066177191edcd0b7e19e7fe44d69ed6dc97b16a420b7aa9070e2b850", "impliedFormat": 1}, {"version": "822a0c843f492ad2dc815080f24d4ddac4817a9df0de8cd35830e88fbbafbbe4", "impliedFormat": 1}, {"version": "467865324b9f66a1b8f68d9350c5aa0e749eec499e4863fe017b16ea8bcaccdf", "impliedFormat": 1}, {"version": "863bd77d5546877e19594759a901cc7b75da8d27336d4351e54413ec12032d09", "impliedFormat": 1}, {"version": "a17a62c94da321c0bf2315c35033e313daf1298a75aa43a01a4daf6937980c01", "impliedFormat": 1}, {"version": "851271a09d3c2db3eab80d64beb468d775a9818df06a826ba58925c900231ccb", "impliedFormat": 1}, {"version": "da2c95cd1f0f9cc19f3dd599b4c8fb0930eccb78a5c73f683e7ea98262d2f55e", "impliedFormat": 1}, {"version": "e40d3ca85fb1362763067506784635aa28863640cf7cf9be9e8c1c521c0fbbd5", "impliedFormat": 1}, {"version": "77a2f84e19aca9d03efdf0c484aba8daad3fd23c70b72e63aca78fadf71b448d", "impliedFormat": 1}, {"version": "00c5b6248c69e66729e5c4acb239db849b1497d7eb111fed3eba979432461ebf", "impliedFormat": 1}, {"version": "8e13abf75e9394f3a4b1d0b3f99468e15f4c7e2115153d2a1ca3c0de035bad1c", "impliedFormat": 1}, {"version": "07097dab1c068118806fecb8544aba3cca30965d0864b1998af1bee326a9990c", "impliedFormat": 1}, {"version": "c490ca6eb9149c28e4f2def6acb1bc058d160edb40fd249cf2a70c206a8cfecc", "impliedFormat": 1}, {"version": "7c9aab9a76abba65aa6389e41707d57ea0288dac9a8b6359465dcb462d2cfaa1", "impliedFormat": 1}, {"version": "97fbe30fd1b61b26f807ae1c78b681b0999af71cd9604c08a1d45e44690ca0c2", "impliedFormat": 1}, {"version": "ef91bf45a3d149db0b9e4e612ed1400c35f6a3d2a09669d1441add612d5f16b8", "impliedFormat": 1}, {"version": "dacebdc0353168f259724bccfd273b892e883baf36cf3dee21cf4178f3ef9ea0", "impliedFormat": 1}, {"version": "5416fb031a72377c3c17faa2041428a5f19f9d46a70b645dda6e3293fd0ca8ce", "impliedFormat": 1}, {"version": "95611472fd03e0992070caa3a5387133e76a079719994d237947f6bcf67f9bca", "impliedFormat": 1}, {"version": "6141d19bfa7698f362e84460856ace80a1eac3eab1956b188427988f4cd8e750", "impliedFormat": 1}, {"version": "1acded787e1fc09fd56c004d3ba5b719916c06b61976338a92a2f04ec05cba5c", "impliedFormat": 1}, {"version": "8fb0d41cd90f47b9148e4a474fb03484d9af1735871321a2f57f456e40a7e319", "impliedFormat": 1}, {"version": "a25cd4cf54bcdd109dd46274e2369fc1cad6d74350b5642441d2b9eef515c3bf", "impliedFormat": 1}, {"version": "af4b9f16e50a0ae803745150e4c091e86ab95f3dac649286af28505258f7a189", "impliedFormat": 1}, {"version": "3d209a6c3c53366b3bcb72dcf04a7ceda57362cae6ac47dbb783321934a0c5ad", "impliedFormat": 1}, {"version": "4766770027d93a5ad1d4cc880cce405b4c6f67c64303ab34b347d6428eb783f2", "impliedFormat": 1}, {"version": "43d2bec085f0fab54d7b9dfa3f5c5ce65e30da6a19d82ed37d1d41867682f86e", "impliedFormat": 1}, {"version": "e5efb9781a0ef18d60cbb8afa261489efd260d87642c095cacba0b09b2684fcf", "impliedFormat": 1}, {"version": "775ca7538a2f9bc674ebe5f3cb8aa8fa346ef4c1faec4c5b13b4784a744854dc", "impliedFormat": 1}, {"version": "c0037c7c6fb8031f7047a1ccdb381762862b48429e9ab07bac8fc35fc5b5dd14", "impliedFormat": 1}, {"version": "af4db63c6e4d55df1ad7f3dabdde31bc30555debf1cd6b79ea65a36c52bf199c", "impliedFormat": 1}, {"version": "d291ffc234a58061b8192f74422f2e51fb87f6d10e82c30a555bccf9641b3e38", "impliedFormat": 1}, {"version": "6d683695e9765b29165bb0823f88755211d48949f0b95a9a4236802afddf41e1", "impliedFormat": 1}, {"version": "8fcd568ba937d867544cd8e726f35a515690ad041387fdebc93d820c8720e08c", "impliedFormat": 1}, {"version": "81a0ff507ece65e130c1dd870ba79b8337c1fd345db7b154a2749282c994d2d5", "impliedFormat": 1}, {"version": "64e2ffc72047548fa3c04095abb9dab48e2eaac169161fd2ed3564dea0c67e57", "impliedFormat": 1}, {"version": "b525d2fc6b694512a877219ebba25d5fa244f99253a5bbe6c6421f8d71b1c806", "impliedFormat": 1}, {"version": "d695f0d65f5fba0e275cf7801399575c272b86e7bf8e70133f8fc03517305b1d", "impliedFormat": 1}, {"version": "0836f15e5e7dcad64fd50d49a39267da34371d1c2b803b38dffcfabcd2ff604e", "impliedFormat": 1}, {"version": "56eff313f885482d44e4aa7cefdd55f7d0d92a91c1ddf9cd73c533abc36f4dff", "impliedFormat": 1}, {"version": "022ff6b725f6ab95b1c4d229893b3047002a9c1fab6798c8fe63797ec1e63dc5", "impliedFormat": 1}, {"version": "5e64d04301aa6ae6bf0f3435d07804889342873ab2875a16c827db9e6543002d", "impliedFormat": 1}, {"version": "0b8c3effe0c65129d493be140da1a83eb61a1e83481d441dd2bc359a926b453e", "impliedFormat": 1}, {"version": "0816c977ef73d99cbb134427a83f91ca6f7fe00eb7544118320d613a85da6879", "impliedFormat": 1}, {"version": "068db2994f5926e888462b0852ada2c24f2cb50028f034f475407957ca51c6cd", "impliedFormat": 1}, {"version": "59106b469557319ad26f40f054861be3fd2cf09911c3b66df280b9340a1d9caf", "impliedFormat": 1}, {"version": "69e8e2dc21b0636f671485867555439facd68ee9e234fc9190c3b42e7f1a74e9", "impliedFormat": 1}, {"version": "5fb0c0cae187f6554769cd4ff36575ddbc43078a4fdf9b17a5c0c25dfa9a9f2b", "impliedFormat": 1}, {"version": "918d99a7aa4b7f5edf2cdcb33c163837a892f43b9e22c10634d61d0a28fc09a2", "impliedFormat": 1}, {"version": "097b0d1e237bfcc97411fcae19a484a717fd4055a48e98ade5cc28b26afd21f6", "impliedFormat": 1}, {"version": "5fb0eef64cb75951f7ae2dc6a704aa0567a25a39a616a5dd10ba7cfbfcf73b78", "impliedFormat": 1}, {"version": "0a649cbc59a47f224d0494a6d5167a803ed049f995ade8423c7cb62bb6a38b64", "impliedFormat": 1}, {"version": "68e25d1a79523b18fae630ca57100ce2dff6c5023376a2f57e9d0d07e1b9b8ef", "impliedFormat": 1}, {"version": "1a505f408bc7d484553b7701f712dc52e1174648baff7d6c9c1f38b5cb83b772", "impliedFormat": 1}, {"version": "b19badf31df455f10cf44fda9f6a0e0b42d6e970ac122b66c5da5d683fa270d4", "impliedFormat": 1}, {"version": "71b6fe5c85eb877c3e3ed2f142b95a69f97905c34f11fc6d9052a4317e7f6bae", "impliedFormat": 1}, {"version": "bd55536c0f989f59af6ca66cbc8121485f978f4e07c3df1688623c5f898058c6", "impliedFormat": 1}, {"version": "dcb868c613ccd06b1a3ff56ee235e5987820c0c8bbd77fedc9af4dcfdd4c54bf", "impliedFormat": 1}, {"version": "f3d1b3cd130e3cd67fe8e06256deb5d678243c6976ea498c81a48e542efb7529", "impliedFormat": 1}, {"version": "772b881836efbdceb7ae8d3ae038f14ec83444397d8429b866312dcd78714dde", "impliedFormat": 1}, {"version": "314d516eb3bf1eda07e898935edcbd1e74739493c8ad444e82181f8a020eef2c", "impliedFormat": 1}, {"version": "8cfced8e57c64563f91e90a76a6df2d8f934c90a425327a9ed5393bc88c27d97", "impliedFormat": 1}, {"version": "67bd754a8775c81794c9fc84b1a1e9fca44a402fa7d93fcdad4ba2d37737d929", "impliedFormat": 1}, {"version": "5128e32c57068eb09d5189eb68681ca7d0e5e4b0cdedecbef9c67689f0970876", "impliedFormat": 1}, {"version": "7fcdedd29146e5a2a6c86eda652f8485a1eeda1b8646825bbf729023f6ea6013", "impliedFormat": 1}, {"version": "86b9b361ce8ea1d9f04e15bbe49e5ac72e5f97d8cfa8592930d32f267729a201", "impliedFormat": 1}, {"version": "671f5e3a931c2737f8dfa43b34c4a320eca27fc6584ecef890ddd7374cee5cb7", "impliedFormat": 1}, {"version": "ff213315eebd3ff05e01b383f704d79d8139aad5cb0d6a13c082f2e29625adbc", "impliedFormat": 1}, {"version": "83ed351a10ef17b7811d3c06fc2775e36b6911278326d55da8d1eef8ff2f29df", "impliedFormat": 1}, {"version": "2f5f146f1d6c04cf89ae0e9b4cf2b064b2ce4319ba6a5bf18ab8fb29db1cfd1a", "impliedFormat": 1}, {"version": "7fc2b96a8465725bf774bd490c383edd5ee3dfe0d38c13551d082cae2de4041e", "impliedFormat": 1}, {"version": "9eaeb6696e4218cb5bded9ee27c3e95589ad4af1fd4b97ccdca43eadd62c94d5", "impliedFormat": 1}, {"version": "fd580a99cb9bb84288da00eea67dce300bdef06d4da2a727c0fc466d2922dca2", "impliedFormat": 1}, {"version": "b82809d4468b6ba4d72437adaab7ca273547c59974e954c48f655a4b1bdca429", "impliedFormat": 1}, {"version": "c6455d4ed4f7337bcb885c61372c7d9b03991995ed73e29023bad502d1336f0a", "impliedFormat": 1}, {"version": "b5e6f0491b5a2002eb9b1146165cf915ee58e0fddf7f2adb5f2aa4bc44b4fb83", "impliedFormat": 1}, {"version": "f534aef095a62fb82f57768fc52995d3e58d95e0a1671b0256a4704802aee818", "impliedFormat": 1}, {"version": "cdc6f1d471882782cdac7442dbdad65aede5f749c09799a84918bd916eb6d6db", "impliedFormat": 1}, {"version": "2475197472c609662f09660e3964a86aa355cea0e671653656800690bb508b7c", "impliedFormat": 1}, {"version": "b4067760d0447747d82b6848b640168d656d0b916c3add2ec94c3c4dea92fc9f", "impliedFormat": 1}, {"version": "c6c591a17f9c0c2821baf15f775f5c7d6dd4a0786365ee9c182d7a97e38ad96a", "impliedFormat": 1}, {"version": "ede44ddf9d274a859e9f1f34333d5f0e8cf2167c3265f81d5280d37b872b4552", "impliedFormat": 1}, {"version": "6317aba53c9152998bb1f8bd593f55730084d05c00c774ff72a3aa4d687a6dbb", "impliedFormat": 1}, {"version": "26f1bd15980b19d925be98afde3918a6a181435b87e9b7c70d15726ecbfff0e5", "impliedFormat": 1}, {"version": "57af4faf6847adff5048f82929b9a7d44619d482f571534539ae96a59bb29d3a", "impliedFormat": 1}, {"version": "874770f851ac64a93aaddfb86a2f901f158711911fee14a98a67fe32533ee48b", "impliedFormat": 1}, {"version": "3d933e519ad9cc8cf811124f50d0bc14223cdea9f17adf155f11d190ceb2a6c8", "impliedFormat": 1}, {"version": "d5dfce61a7bf994d2cb711af824efa4de9afa5854d34e6725b9c69d925b6b2dc", "impliedFormat": 1}, {"version": "f77d1e10417bf43f8fa5d18916935f342d4d443e371206ede7239faaf9abbbb8", "impliedFormat": 1}, {"version": "c94e0b8815b72ba924c6b8aa666b25903d949a7ab0d38ed84e4bf65da3d06a3b", "impliedFormat": 1}, {"version": "15db84e660fdcd8468f23973ab83c31d7fd28bdddb30b0aed16cfa051aafe900", "impliedFormat": 1}, {"version": "7c01cbfe181c0e10044831b899de6c2eec4fba32de1f1cca12742d2333c1345b", "impliedFormat": 1}, {"version": "62cb1636513ef26d3ea83fb5d2369cf8569d04aa30d8fd7f5327d0e10841635d", "impliedFormat": 1}, {"version": "8282a076b07dc3dc6b2265377627ab3860cb6a1bcbae85a5a4006dec4c9f0066", "impliedFormat": 1}, {"version": "b273c241dd08c6276fd35be413c64508ae50f847fa052bf7781799b51da8e9e9", "impliedFormat": 1}, {"version": "3bc0bbef6d7fb63002fe80167db350b9677cfce5872c0cc7ecec42ba8248ded6", "impliedFormat": 1}, {"version": "4880c6a85442934b81f3b1a92cb6b43df36f8c1b56b6822eb8cbc8c10c438462", "impliedFormat": 1}, {"version": "1bfdd8c1710a3d1654746ca17f512f4a162968a28e1be1a3a1fdd2a8e5bf385f", "impliedFormat": 1}, {"version": "5405aedafdf272dde53b89036199aaed20d81ddc5ec4bea0cb1ab40232fff3fe", "impliedFormat": 1}, {"version": "db2ee45168db78cc83a4368546e0959318374d7256cbd5fa5692a430d5830a59", "impliedFormat": 1}, {"version": "49993b0eaa14d6db6c334ef0e8b1440c06fee2a21ffd4dea64178880bd3d45a2", "impliedFormat": 1}, {"version": "fb9d9dc0a51cb4014d0e5d5f230ec06ffc4eb6caae6eecfe82ea672b7f3c6967", "impliedFormat": 1}, {"version": "84f44079a0793547d3a629feb8f37d8ef6d07cb5bb5fdeefd887f89e9be871f6", "impliedFormat": 1}, {"version": "295c5ec088a1bfc286e8dbdc9807958588979988cd7a74ad32be774a6f6ea512", "impliedFormat": 1}, {"version": "f15129c62ed04410ac0a3326ae6fa5ef7229bbb1b0cbfa252b5c558505a38253", "impliedFormat": 1}, {"version": "4bf500d9a554d43cb9133d60f1b3f58ca98b0f794486d1377f3effc551b40faf", "impliedFormat": 1}, {"version": "536f6a9208c89eb8f0a5eeda629175b0fa62ccd22e387af7f35297fa2af6897d", "impliedFormat": 1}, {"version": "8c95fe5a655ea1c78f0335f8da58e70d98e72fe915987c3b61c6df49d6e276d1", "impliedFormat": 1}, {"version": "4bd434d3055d1b4588f9d7522d44c43611341de7227db9718a700703c608e822", "impliedFormat": 1}, {"version": "935507b695f420fddff2d41ddc12ff3935931a3f26d6aa65afbb276bfdf51cb4", "impliedFormat": 1}, {"version": "e851c14c9dbe365592f5084c76d4b801e2f80302f82cebbe7c2b86095b3ae08a", "impliedFormat": 1}, {"version": "b5c90d931d285d9d1c4cb92d71f2719e28caaa9ca062072d0bb3b69300b436c2", "impliedFormat": 1}, {"version": "40b3e953e9ea51a86a1e5b60a2355eeb780f2f8ce895ece252910d3e0a033a16", "impliedFormat": 1}, {"version": "0264b432aace8398f174e819a0fc4dc196d5aed49ae65aae071fc2ec8e6dc029", "impliedFormat": 1}, {"version": "3b29bb23855a1924264c3a30b5c73b00c52a57c2ffb5f91c48c9572e71048f19", "impliedFormat": 1}, {"version": "8b9b2e76db07d8926bcc432c9bdfb38af390568951b39fe122d8251b954f9ed2", "impliedFormat": 1}, {"version": "96e85c6fa102741a25418ab2c8f740c994e27ea86fd6518a17ec01a84b64dd5c", "impliedFormat": 1}, {"version": "9525b28a4fa959c8d8c7d6815f842f78c67b40def9160afdced5c9daf14cd4a8", "impliedFormat": 1}, {"version": "0e59a6944a52f52138315b6658fb1d217fa017b7abec12006c491d51e07fb56d", "impliedFormat": 1}, {"version": "cfa8acfeb9d68702aa6249b7295ca73ea598e441f014cd4184b6e2a3ea9a275c", "impliedFormat": 1}, {"version": "21b0c616f61cd6699135a34a500f7df30022abf9358ba612f10668ea3c988e00", "impliedFormat": 1}, {"version": "9ad1d0b171f7bb9f484ad156e97f0d8e760a5fee13e342831669c7b2d1137a30", "impliedFormat": 1}, {"version": "7ccadd4ba126bb2c0564bfb85ddd7d084aa5f2880cc2d0149fbe183fd5ceb6d1", "impliedFormat": 1}, {"version": "ebbde5a8a356a1547ac6ecdfba7547036a5ada116011cb96634c32df1cf69084", "impliedFormat": 1}, {"version": "e703eded767e3a944ac1f7c58c201a0821da1d68c88d6ba94bb985a347c53e42", "impliedFormat": 1}, {"version": "99953f3f1f9deae755b97ed3f43ce2bee2ae1324c21c1e5fa9285c0fe7b5077f", "impliedFormat": 1}, {"version": "2afd452bfa6ebaacbead1ca5d8ab6eda3064d1ea7df60f2f8a2e8e69b40259e9", "impliedFormat": 1}, {"version": "dae0f3382477d65621b86a085bdb0caabf49e6980e9f50ee1506b7466c4d678d", "impliedFormat": 1}, {"version": "e5793b3f4cbd73c841790264db591d3abe9bd09128302a2901fedd2353ab24d5", "impliedFormat": 1}, {"version": "41ed74193a13f64a53705a83e243235920fd58d4b115b4a9f5d122362cda7662", "impliedFormat": 1}, {"version": "478e31b207faa7110b04f6a406240f26b06243eb2d2cff3234c3fc8dd075bf6c", "impliedFormat": 1}, {"version": "ea917cdbfb87d11cd2f8b03e357b22b1114d0ba39ce4ce52b1a4f0dc10c6c20a", "impliedFormat": 1}, {"version": "3ef0c5634d9aabee346f9ba056c1c5d977f2e811f6d13c082614c9062cd4b624", "impliedFormat": 1}, {"version": "1ddb49c7f8fc4b9e4da2d5ddca91b4e2763fe7d17aa79940bd60406f3e2739bd", "impliedFormat": 1}, {"version": "d5b01eab562dc40986a5ceb908519dc7f02a7ded2bcb74318317a75714dbc54c", "impliedFormat": 1}, {"version": "b19ef44e991aa150a19a9f84be1fd1c4d86496241300fd904216762246700623", "impliedFormat": 1}, {"version": "87df6cf2565a88dae3ec50e403e9ef6b434ad3e34d922fe11924299018b38e58", "impliedFormat": 1}, {"version": "9d999d30b52fb0b916f7a64c468f6d5c7a994e0c1ef74d363562e9bda3cb8b99", "impliedFormat": 1}, {"version": "9b1b05f88ded21046391276ff60d2d987bf160d77b40399e07b7bdbfe2e38b31", "impliedFormat": 1}, {"version": "628177f7eb0314f0189e4d90f663233606b3936ce391c7f98da46105ae402c65", "impliedFormat": 1}, {"version": "3c80bf6873eb3b95cd590aab8eb1612f0f7cef6a30b3f49535844f7cecd99351", "impliedFormat": 1}, {"version": "da367ede4ebd5ff4cb1cf9e6bc8eb35848b23c57c22c53360e53dc772c7be8f9", "impliedFormat": 1}, {"version": "4337acbd8896efb7e7d8d6e0eca78607fc7c1a9ad2bb228240f13f97b3492f1f", "impliedFormat": 1}, {"version": "505c7800f8195961302dee715870b7212bdfb667e5e47de76447151dd35a40f1", "impliedFormat": 1}, {"version": "cf5a3eed6cd493d198b0c1eacf70486d8bd527fc411d57660caf2c93b5ea0fb6", "impliedFormat": 1}, {"version": "900e344adae3c65076c9ba4ee1a77c6db19fb0c7e54d7ce23c28ff8d272cba26", "impliedFormat": 1}, {"version": "bcc5186a38d1eecf60b2c4d1e3eb9abd8ab91cb492f384a9d2ed7bcda2abd0d5", "impliedFormat": 1}, {"version": "0ec1b41954fea9def7d9d87e0f3beea2ba3ec5b7beb769f308cfe32ad2968669", "impliedFormat": 1}, {"version": "51189c085256f11da13b22792f1d7c928f8a8e9d9b6c7b38e956e72a51ef8219", "impliedFormat": 1}, {"version": "504f509e23f2ab3a8018533925c034a340fbce4af9e77a1f71a8ddffbe0c19fa", "impliedFormat": 1}, {"version": "635c049483e13e1dc8bee72dde300c40d350046cff59b202d41a12ec8c733d27", "impliedFormat": 1}, {"version": "7fd8d5f70ea745e1a0338de7aaacd9bd6ff086ce6de75dcf91749c77d1e23831", "impliedFormat": 1}, {"version": "78d2a7795bfd2be490937e8b01968a0acca8a6bdf5933570bc013806049d4175", "impliedFormat": 1}, {"version": "db49833b6e9aa54b535076f40615349a7465005367a787b50ba7b92421e26442", "impliedFormat": 1}, {"version": "6a936fc917de40c44ca81331ee7d7a71dc30ae1895871e7be7b6ed85d96cc41f", "impliedFormat": 1}, {"version": "bdd2a764cf87c4ab1efd7084597d1ca4ba17f6b6496553095ecca5a14b5d4278", "impliedFormat": 1}, {"version": "ddef8e6676fd572ee3de174ad28df05c7b3803542d7318482b8f98779ff25612", "impliedFormat": 1}, {"version": "34eae3bc7f5bfb515d2ec163ccd4b63fdb73ad7f66564707686d84f42a8b7c35", "impliedFormat": 1}, {"version": "d240d106cf9bc3c0efdb323d807b944ce16ac5d837ecef5b75f1e66d606b2a72", "impliedFormat": 1}, {"version": "639d5a26be297431e0bcc9f71f969fd7d84319fc03b5e1c672ea10fb0094c616", "impliedFormat": 1}, {"version": "770c3e6367c2802c027c0b1f86928f288e11ad77ac2f454d7f682460eab30a0c", "impliedFormat": 1}, {"version": "c9dd2760e0419a059cf733c38ef5d44eeca3fc647f9c201d88656e5040f5a3a7", "impliedFormat": 1}, {"version": "16766b8f3d1bba66ac8167e6407be6c490d4462e802f67c140b1174869db5b67", "impliedFormat": 1}, {"version": "f9267391788ac81ca54dfae32c5d86e99a19abaee9b172b2f8d98a3c2b578a2f", "impliedFormat": 1}, {"version": "92441638c0fa88072ef9f7b296a30e806bac70219ce2736ef33c8941259d9b70", "impliedFormat": 1}, {"version": "8774efbaf39f9ea3a0ff5b1c662c224babee5abb3d754796278e30eb2e51ae3c", "impliedFormat": 1}, {"version": "e634b47a7d3f9468572a7c9af1fe2f52687ee1afb23ba5568205a7a4c55662ef", "impliedFormat": 1}, {"version": "1cbef47ee169c717a1ef7ea91b15582c61ac721fd5f5671de95c3df9f026db9a", "impliedFormat": 1}, {"version": "0db0ee49f803c9b901dfe06be9c8fb6a1c05f98664ca34c68e0da575eae76f2b", "impliedFormat": 1}, {"version": "4b745fcadf040899979b6b26e24aca6d2fa2bbe52a919d67f717bfe0339354a3", "impliedFormat": 1}, {"version": "bc57f3550b3fd3b7d31b9a278d0b491dd45d170e37c4046a3105fdea9ebe5f89", "impliedFormat": 1}, {"version": "b5f7093d62a228669dd56edd0bcb86a0cf0b46db4816a3967b4632503c21b93c", "impliedFormat": 1}, {"version": "4d70bbb1f35f345b2c2e1b5c9b8174d5397bba76ffef12656bca16ce9a1830d3", "impliedFormat": 1}, {"version": "a004fc80aa8f78dfb1d47b0e098fe646e759311c276b6b27404f5e356528f22d", "impliedFormat": 1}, {"version": "c8933d9afe6c5ee7ecbeec5aa01f6b37d3c2be2f7dd203ee75ee4850164007cb", "impliedFormat": 1}, {"version": "b1129b38f1eea70951ece3ccd1cc3e1d094379b64d3958ba8ce55b0ec0083434", "impliedFormat": 1}, {"version": "b2bb10f992cfd1cf831eb005311a80f7f28bc14cfac5883f17e75f758d1354e1", "impliedFormat": 1}, {"version": "58b621b924324874a67e92d7626809fd4b72fc079ce909f6da7097654026af00", "impliedFormat": 1}, {"version": "149288ae23bb3b31ffe5cfb7eea669fc6872e41901d60be932af2581601fc70f", "impliedFormat": 1}, {"version": "01a0fd262c8fdc6c91078255c4fe2f8602fd4fe4c753b2eae88537585b21dddf", "impliedFormat": 1}, {"version": "deb69e6754a61784daadc35b318544b0aa69048ebfb142073c62b7f46bb1d5d0", "impliedFormat": 1}, {"version": "60eef77c9b5cec20516907628f849845975a8137773ddb0bcb53fc2ea7d28870", "impliedFormat": 1}, {"version": "67bcdcbd8cece34ae28180c636908af1b118fa9603d0d4b7dea877156d4de519", "impliedFormat": 1}, {"version": "5a1c2cee26d1f8d9bb15b334f5b2df7de27a3944bff9ccf71d3b69c588612bda", "impliedFormat": 1}, {"version": "a04d60b205af1f28461f3d2f5a8222ec2d8af54d436bc53a0460756e07e4207d", "impliedFormat": 1}, {"version": "14c85d4debb2e0c8939f81b85cb9ab4543f70c8fe53be5fb5caf1192677c8ca4", "impliedFormat": 1}, {"version": "c507cdc9757c048620ff08a85b9cf6278598eb1738d729fdbfa1e387a35e639a", "impliedFormat": 1}, {"version": "4a4807c3096f49a463476742e3b5d23ccf0e087e43c017891c332ae5b8ad667d", "impliedFormat": 1}, {"version": "c611af558c5d19fa477f1b03ceac7b0ae28fe5ad4f8bc61e8ad64c46f97e86e2", "impliedFormat": 1}, {"version": "0cec41f583efa1f1033a4d546d926ee949756f19040bb65807c5a3ab6f3b8449", "impliedFormat": 1}, {"version": "73b1eda15491d4f3052d6fac202190e76d6453fce832034bd29901cb198448b9", "impliedFormat": 1}, {"version": "08c66989383183f3d7c43346617c8f466bef28f1e3eb4da829316d548cdbdf80", "impliedFormat": 1}, {"version": "1f283476bbeaa589fe644fe6ba9da223baf118ecd4756863deae7362b246aff3", "impliedFormat": 1}, {"version": "0a8f91ace4d1803eb2a50079c9e233fb262b0027d19aa250eb7ecbf6319e52d6", "impliedFormat": 1}, {"version": "65bab52912be03b374ab591d73ee40aff3a465ac20bc0f2024b4c80ac5ce8397", "impliedFormat": 1}, {"version": "6a647bf0620a4a7777527c688c62636a503e8b4d5e680037503066dd2af6d0dd", "impliedFormat": 1}, {"version": "f1466e4d708815280c849956a506e132b7dc243907b9c8e07d52862e32dfcd91", "impliedFormat": 1}, {"version": "cb4b99f8e47f57df841c95fcb1afc28488a2b5442e3524f6261e611b86105331", "impliedFormat": 1}, {"version": "7c5fc61fc40a9f3aa3a09fd867536ff94a93b16f4ae99f1fb748fae6e13ae8bc", "impliedFormat": 1}, {"version": "473d9ca5b242db0471d418336f410922eadd290679914f37ef21ee26dbeee2b4", "impliedFormat": 1}, {"version": "2ffeb6ad0b074d1cfa3dc9671dad062b08129d1e8a8988b727dd2ce9fd4298d8", "impliedFormat": 1}, {"version": "fa1d4332a68d84300895af592811f65f5f1d725ed0664f17d5c215a63408b6b4", "impliedFormat": 1}, {"version": "7a09768c36d8b7d8e44b6085031712559362b28a54f133b803bed19408676cdf", "impliedFormat": 1}, {"version": "f0b807278b2619fbe0acb9833bd285acabbf31da3592da949f4668a2e4bcbcf0", "impliedFormat": 1}, {"version": "bc6419ca69c35169941d9d0f7a15c483a82ac601c3448257f29a1123bc2399e1", "impliedFormat": 1}, {"version": "45f530610645ca6e25621ce8e7b3cf6c28cd5988871bc68b3772488bd8e45c25", "impliedFormat": 1}, {"version": "2d3e715ca71765b491ae8bd76257e8ccfe97201c605dadc4e6532bb62e4f6eee", "impliedFormat": 1}, {"version": "c519419c11e61347181ba3b77e8d560d8cc7614b6231cacefe206b41474792d4", "impliedFormat": 1}, {"version": "24823640771cf82865c3b1cb48a8a88119b69e56aef594171cc0570f35f60b8a", "impliedFormat": 1}, {"version": "30398045bda704d03d23e78a37095aa56e69ab2dd8bb7304b15df9e183b9800a", "impliedFormat": 1}, {"version": "9a816fe54ea736ecf02b6865c10157724cdb5ba3f57ead02d9216b2dd4bd0d5f", "impliedFormat": 1}, {"version": "a67582f2933f5b6faebba3484c99e78b529aa016369b768021726e400c93ddb8", "impliedFormat": 1}, {"version": "96cd7367cc076d36d9f10cbe34b91e94467caf9b64a7a0fe1c4f6c8287e0a1b5", "impliedFormat": 1}, {"version": "17c7be2c601e4b7e6292932997e491ff874418bef9ee6137e69ea6ef497e0e5d", "impliedFormat": 1}, {"version": "eb7ed3b69718cf40c1ab8ce9a0e917819e0ef0b7480ba2890cddbb94a1386b10", "impliedFormat": 1}, {"version": "7a7cec0720ee6d20e08fa9def697b149a94db1763bbec6e1ab5da8d7726ebddc", "impliedFormat": 1}, {"version": "c024677c477a9dd20e7aba894c2f3e6ef81c4076af932a7fc00c210543cd53bc", "impliedFormat": 1}, {"version": "f32fe1688d2369c07d8d86317d98685efe0a01b9e766f944f3561dff306cbaab", "impliedFormat": 1}, {"version": "f0313fb773fa3bbe850bcc595421b3a71a441c3e264860680dc0d05a7c54b565", "signature": "3eb972ae325aa293fdb6077cdf956f209ee6ea34b4e874ff7ec8686b3079972f"}, {"version": "02c5a5a74c2823dfc7972eac140c0b84b6133423f48e80b818630813ab6ebfa0", "signature": "eef745885cd8ba71e5e8581fe6c7dd9dacb1d0db7410544b788a7c918ac8b574"}, {"version": "0f5d331938aa977ac7c29c2f341ca3da233c12d4c7407110ec1d5f48166dd509", "signature": "dd89095b82057c4501d9f57cd41cc3d151ab97d51e568125cc9ae917e22f538d"}, {"version": "16f52ca7e846adae2833bf7bd2097ba927550ddcae5bf148e07b08a982725705", "signature": "26e8541669798e8148afe27599583e62b689623bba6f3235a36775cd291e7e5d"}, {"version": "75df432476a2976a6c07a8ce069264327e0afc874b30e4ebe0a2b544c96f22a7", "signature": "4fdacdddd03c419b4d6376ab469b9fd351f3677dd43220a0c51a735d353b3c63"}, {"version": "aade2326887caea10b20ffa3d0fe99f2ec17e8df68f9995f648fe725f3b3b049", "signature": "b46f1e6ceb32dc9f0fd8a6894e1771e9e71c88334d1f4f17d891e96a6b74ad38"}, {"version": "726ded8368f78c5e8fb5d7a32979131401756cc0584c78450d9e8a8ed36dbaa5", "signature": "863cc5750d50dbb0f6bb601abc214003aafb4390d5af69d833000336b939740f"}, {"version": "32a92aac48ebcb653bf912c69a03c8ed5e14bc2bcfc730ee9e2bfe2a51f1e1b8", "signature": "c80b4920cdebbd92bc499ab36fa2e700bac912ae4c2706c67914ac999da779ed"}, {"version": "d925688d1b73f6681359da69f5c09a34ed886f74e1a7e90e70d0a8ea8d9d4633", "signature": "12928c086abf84f2a4481c423f3cbfe2ab7754a9730ecb1c41b5c3f79addaf0d"}, {"version": "d5bac9dd8763bf19b8e64a925fa0ca6224a79c1a061b9c8fc633baef9619084d", "signature": "3a8523bfa20e149df671c3166d3c2e0c8e5b9f015b121a6855deedf209096b5e"}, {"version": "263ede69d4fb6225ecbfd5cf1d202964f7f77d2489e81152417e8b402a118088", "signature": "0c77434a78b66a479b58fbfa5f89b56e956999624c7e387ce9b47eea0cc2fb6a"}, {"version": "9ac2b148ae8e6370c79a3d2c70b4c551cf4a256f3d9641801b61bf086cc6dabc", "signature": "b8ca88ed5d7b8aa54b9f4bd2f3bb04d059366737629592070a873aaacfdc43c1"}, {"version": "8edbf87f8be7a678c5ac0f8de01bbbc6fc612a9ef7f61d7f1911310966e0f0c3", "signature": "37e40c971f55dc0f1df199e66741f62b970ab299a52e9dee0960a7a176623b09"}, {"version": "045fdac2143f116b28f2633f5720fe7b3d4f68c38cd1d7938366339854dd736b", "signature": "13abd7ad1ba1a9bbc7795c76322a930975401c8f0efb33c59cf2ce6ebb3a21cc"}, {"version": "30e865c7efa34a9633181fb3783ae339a426fbbab54851550d47d2550051a651", "signature": "563125050e36cbf651e309beaec4345d8272137b2751d2f1c7d09dd30bc226a9"}, {"version": "07f41b9a703f4d28c9c3604fa4a8a333e2f807a93c8bcc1d5d1d0148c77ca1d9", "signature": "95d81889c0f26b1628c66635c397a3631e81837a4fe945b17a05379f82202c3f"}, {"version": "3e170f9dcee2e94ab4b480bd2111af2333a61d1018f33d9cbd47ac1ef28a8bf7", "signature": "1999d1c1dbe909316b5095fece7d98e1d1f79a0a36b23bb01d6b1215c2cae73d"}, {"version": "0042a4b2dd8b6217ebce2e02ff02a48fce804cc9d624ae1689cb3bac57e99a67", "signature": "99ec979895124744640409da511bb78ba169222bc666b09d51f5fcea34f81e6e"}, {"version": "96d14f21b7652903852eef49379d04dbda28c16ed36468f8c9fa08f7c14c9538", "impliedFormat": 1}, {"version": "736097ddbb2903bef918bb3b5811ef1c9c5656f2a73bd39b22a91b9cc2525e50", "impliedFormat": 1}, {"version": "4340936f4e937c452ae783514e7c7bbb7fc06d0c97993ff4865370d0962bb9cf", "impliedFormat": 1}, {"version": "b70c7ea83a7d0de17a791d9b5283f664033a96362c42cc4d2b2e0bdaa65ef7d1", "impliedFormat": 1}, {"version": "7fa8d75d229eeaee235a801758d9c694e94405013fe77d5d1dd8e3201fc414f1", "impliedFormat": 1}], "root": [406, [410, 420], [422, 424], 493, 494, [498, 503], [510, 515], [517, 546], [548, 623], 625, [627, 635], [639, 645], [971, 988]], "options": {"allowJs": true, "esModuleInterop": true, "jsx": 1, "module": 99, "skipLibCheck": true, "strict": true, "target": 1}, "referencedMap": [[406, 1], [408, 2], [646, 3], [647, 3], [648, 3], [649, 3], [651, 3], [650, 3], [652, 3], [658, 3], [653, 3], [655, 3], [654, 3], [656, 3], [657, 3], [659, 3], [660, 3], [663, 3], [661, 3], [662, 3], [664, 3], [665, 3], [666, 3], [667, 3], [669, 3], [668, 3], [670, 3], [671, 3], [674, 3], [672, 3], [673, 3], [675, 3], [676, 3], [677, 3], [678, 3], [701, 3], [702, 3], [703, 3], [704, 3], [679, 3], [680, 3], [681, 3], [682, 3], [683, 3], [684, 3], [685, 3], [686, 3], [687, 3], [688, 3], [689, 3], [690, 3], [696, 3], [691, 3], [693, 3], [692, 3], [694, 3], [695, 3], [697, 3], [698, 3], [699, 3], [700, 3], [705, 3], [706, 3], [707, 3], [708, 3], [709, 3], [710, 3], [711, 3], [712, 3], [713, 3], [714, 3], [715, 3], [716, 3], [717, 3], [718, 3], [719, 3], [720, 3], [721, 3], [724, 3], [722, 3], [723, 3], [725, 3], [727, 3], [726, 3], [731, 3], [729, 3], [730, 3], [728, 3], [732, 3], [733, 3], [734, 3], [735, 3], [736, 3], [737, 3], [738, 3], [739, 3], [740, 3], [741, 3], [742, 3], [743, 3], [745, 3], [744, 3], [746, 3], [748, 3], [747, 3], [749, 3], [751, 3], [750, 3], [752, 3], [753, 3], [754, 3], [755, 3], [756, 3], [757, 3], [758, 3], [759, 3], [760, 3], [761, 3], [762, 3], [763, 3], [764, 3], [765, 3], [766, 3], [767, 3], [769, 3], [768, 3], [770, 3], [771, 3], [772, 3], [773, 3], [774, 3], [776, 3], [775, 3], [777, 3], [778, 3], [779, 3], [780, 3], [781, 3], [782, 3], [783, 3], [785, 3], [784, 3], [786, 3], [787, 3], [788, 3], [789, 3], [790, 3], [791, 3], [792, 3], [793, 3], [794, 3], [795, 3], [796, 3], [797, 3], [798, 3], [799, 3], [800, 3], [801, 3], [802, 3], [803, 3], [804, 3], [805, 3], [806, 3], [807, 3], [812, 3], [808, 3], [809, 3], [810, 3], [811, 3], [813, 3], [814, 3], [815, 3], [817, 3], [816, 3], [818, 3], [819, 3], [820, 3], [821, 3], [823, 3], [822, 3], [824, 3], [825, 3], [826, 3], [827, 3], [828, 3], [829, 3], [830, 3], [834, 3], [831, 3], [832, 3], [833, 3], [835, 3], [836, 3], [837, 3], [839, 3], [838, 3], [840, 3], [841, 3], [842, 3], [843, 3], [844, 3], [845, 3], [846, 3], [847, 3], [848, 3], [849, 3], [850, 3], [851, 3], [853, 3], [852, 3], [854, 3], [855, 3], [857, 3], [856, 3], [970, 4], [858, 3], [859, 3], [860, 3], [861, 3], [862, 3], [863, 3], [865, 3], [864, 3], [866, 3], [867, 3], [868, 3], [869, 3], [872, 3], [870, 3], [871, 3], [874, 3], [873, 3], [875, 3], [876, 3], [877, 3], [879, 3], [878, 3], [880, 3], [881, 3], [882, 3], [883, 3], [884, 3], [885, 3], [886, 3], [887, 3], [888, 3], [889, 3], [891, 3], [890, 3], [892, 3], [893, 3], [894, 3], [896, 3], [895, 3], [897, 3], [898, 3], [900, 3], [899, 3], [901, 3], [903, 3], [902, 3], [904, 3], [905, 3], [906, 3], [907, 3], [908, 3], [909, 3], [910, 3], [911, 3], [912, 3], [913, 3], [914, 3], [915, 3], [916, 3], [917, 3], [918, 3], [919, 3], [920, 3], [922, 3], [921, 3], [923, 3], [924, 3], [925, 3], [926, 3], [927, 3], [929, 3], [928, 3], [930, 3], [931, 3], [932, 3], [933, 3], [934, 3], [935, 3], [936, 3], [937, 3], [938, 3], [939, 3], [940, 3], [941, 3], [942, 3], [943, 3], [944, 3], [945, 3], [946, 3], [947, 3], [948, 3], [949, 3], [950, 3], [951, 3], [952, 3], [953, 3], [956, 3], [954, 3], [955, 3], [957, 3], [958, 3], [960, 3], [959, 3], [961, 3], [962, 3], [963, 3], [964, 3], [965, 3], [967, 3], [966, 3], [968, 3], [969, 3], [359, 5], [409, 6], [407, 5], [421, 7], [504, 8], [508, 5], [509, 9], [505, 10], [506, 11], [507, 11], [989, 5], [496, 12], [495, 5], [991, 5], [992, 13], [137, 14], [138, 14], [139, 15], [97, 16], [140, 17], [141, 18], [142, 19], [92, 5], [95, 20], [93, 5], [94, 5], [143, 21], [144, 22], [145, 23], [146, 24], [147, 25], [148, 26], [149, 26], [151, 27], [150, 28], [152, 29], [153, 30], [154, 31], [136, 32], [96, 5], [155, 33], [156, 34], [157, 35], [189, 36], [158, 37], [159, 38], [160, 39], [161, 40], [162, 41], [163, 42], [164, 43], [165, 44], [166, 45], [167, 46], [168, 46], [169, 47], [170, 5], [171, 48], [173, 49], [172, 50], [174, 51], [175, 52], [176, 53], [177, 54], [178, 55], [179, 56], [180, 57], [181, 58], [182, 59], [183, 60], [184, 61], [185, 62], [186, 63], [187, 64], [188, 65], [84, 5], [194, 66], [195, 67], [193, 3], [191, 68], [192, 69], [82, 5], [85, 70], [282, 3], [993, 5], [516, 5], [98, 5], [83, 5], [990, 71], [624, 3], [91, 72], [362, 73], [366, 74], [368, 75], [215, 76], [229, 77], [333, 78], [261, 5], [336, 79], [297, 80], [306, 81], [334, 82], [216, 83], [260, 5], [262, 84], [335, 85], [236, 86], [217, 87], [241, 86], [230, 86], [200, 86], [288, 88], [289, 89], [205, 5], [285, 90], [290, 91], [377, 92], [283, 91], [378, 93], [267, 5], [286, 94], [390, 95], [389, 96], [292, 91], [388, 5], [386, 5], [387, 97], [287, 3], [274, 98], [275, 99], [284, 100], [301, 101], [302, 102], [291, 103], [269, 104], [270, 105], [381, 106], [384, 107], [248, 108], [247, 109], [246, 110], [393, 3], [245, 111], [221, 5], [396, 5], [637, 112], [636, 5], [399, 5], [398, 3], [400, 113], [196, 5], [327, 5], [228, 114], [198, 115], [350, 5], [351, 5], [353, 5], [356, 116], [352, 5], [354, 117], [355, 117], [214, 5], [227, 5], [361, 118], [369, 119], [373, 120], [210, 121], [277, 122], [276, 5], [268, 104], [296, 123], [294, 124], [293, 5], [295, 5], [300, 125], [272, 126], [209, 127], [234, 128], [324, 129], [201, 71], [208, 130], [197, 78], [338, 131], [348, 132], [337, 5], [347, 133], [235, 5], [219, 134], [315, 135], [314, 5], [321, 136], [323, 137], [316, 138], [320, 139], [322, 136], [319, 138], [318, 136], [317, 138], [257, 140], [242, 140], [309, 141], [243, 141], [203, 142], [202, 5], [313, 143], [312, 144], [311, 145], [310, 146], [204, 147], [281, 148], [298, 149], [280, 150], [305, 151], [307, 152], [304, 150], [237, 147], [190, 5], [325, 153], [263, 154], [299, 5], [346, 155], [266, 156], [341, 157], [207, 5], [342, 158], [344, 159], [345, 160], [328, 5], [340, 71], [239, 161], [326, 162], [349, 163], [211, 5], [213, 5], [218, 164], [308, 165], [206, 166], [212, 5], [265, 167], [264, 168], [220, 169], [273, 170], [271, 171], [222, 172], [224, 173], [397, 5], [223, 174], [225, 175], [364, 5], [363, 5], [365, 5], [395, 5], [226, 176], [279, 3], [90, 5], [303, 177], [249, 5], [259, 178], [238, 5], [371, 3], [380, 179], [256, 3], [375, 91], [255, 180], [358, 181], [254, 179], [199, 5], [382, 182], [252, 3], [253, 3], [244, 5], [258, 5], [251, 183], [250, 184], [240, 185], [233, 103], [343, 5], [232, 186], [231, 5], [367, 5], [278, 3], [360, 187], [81, 5], [89, 188], [86, 3], [87, 5], [88, 5], [339, 189], [332, 190], [331, 5], [330, 191], [329, 5], [370, 192], [372, 193], [374, 194], [638, 195], [376, 196], [379, 197], [405, 198], [383, 198], [404, 199], [385, 200], [391, 201], [392, 202], [394, 203], [401, 204], [403, 5], [402, 7], [357, 205], [497, 206], [426, 5], [432, 207], [425, 5], [429, 5], [431, 208], [428, 209], [492, 210], [455, 211], [451, 212], [466, 213], [456, 214], [463, 215], [450, 216], [464, 5], [462, 217], [459, 218], [460, 219], [457, 220], [465, 221], [433, 209], [434, 222], [547, 223], [445, 224], [442, 225], [443, 226], [444, 227], [446, 228], [453, 229], [472, 230], [468, 231], [467, 232], [471, 233], [469, 234], [470, 234], [447, 235], [449, 236], [448, 237], [452, 238], [439, 239], [454, 240], [438, 241], [440, 242], [437, 243], [441, 244], [436, 245], [473, 234], [476, 246], [474, 247], [475, 248], [477, 249], [479, 250], [478, 251], [482, 252], [480, 251], [481, 253], [483, 234], [491, 254], [484, 251], [485, 234], [458, 255], [461, 256], [435, 5], [486, 234], [487, 257], [489, 258], [488, 259], [490, 260], [427, 261], [430, 262], [626, 3], [79, 5], [80, 5], [13, 5], [14, 5], [16, 5], [15, 5], [2, 5], [17, 5], [18, 5], [19, 5], [20, 5], [21, 5], [22, 5], [23, 5], [24, 5], [3, 5], [25, 5], [26, 5], [4, 5], [27, 5], [31, 5], [28, 5], [29, 5], [30, 5], [32, 5], [33, 5], [34, 5], [5, 5], [35, 5], [36, 5], [37, 5], [38, 5], [6, 5], [42, 5], [39, 5], [40, 5], [41, 5], [43, 5], [7, 5], [44, 5], [49, 5], [50, 5], [45, 5], [46, 5], [47, 5], [48, 5], [8, 5], [54, 5], [51, 5], [52, 5], [53, 5], [55, 5], [9, 5], [56, 5], [57, 5], [58, 5], [60, 5], [59, 5], [61, 5], [62, 5], [10, 5], [63, 5], [64, 5], [65, 5], [11, 5], [66, 5], [67, 5], [68, 5], [69, 5], [70, 5], [1, 5], [71, 5], [72, 5], [12, 5], [76, 5], [74, 5], [78, 5], [73, 5], [77, 5], [75, 5], [114, 263], [124, 264], [113, 263], [134, 265], [105, 266], [104, 267], [133, 7], [127, 268], [132, 269], [107, 270], [121, 271], [106, 272], [130, 273], [102, 274], [101, 7], [131, 275], [103, 276], [108, 277], [109, 5], [112, 277], [99, 5], [135, 278], [125, 279], [116, 280], [117, 281], [119, 282], [115, 283], [118, 284], [128, 7], [110, 285], [111, 286], [120, 287], [100, 288], [123, 279], [122, 277], [126, 5], [129, 289], [410, 290], [411, 290], [415, 291], [414, 290], [413, 290], [412, 290], [591, 292], [592, 293], [593, 3], [595, 294], [596, 295], [594, 293], [597, 3], [598, 296], [599, 293], [600, 296], [601, 296], [602, 293], [603, 293], [604, 296], [606, 297], [605, 296], [607, 296], [608, 296], [609, 298], [610, 298], [611, 296], [612, 299], [574, 296], [613, 300], [614, 298], [615, 301], [616, 293], [618, 302], [619, 303], [620, 304], [621, 3], [622, 3], [627, 305], [628, 3], [572, 3], [573, 3], [629, 3], [630, 306], [617, 293], [631, 3], [632, 3], [633, 3], [590, 3], [625, 307], [634, 3], [623, 3], [635, 3], [589, 308], [418, 309], [422, 310], [419, 311], [416, 312], [639, 313], [501, 314], [502, 314], [503, 315], [510, 316], [511, 315], [512, 317], [513, 318], [514, 319], [515, 320], [423, 206], [520, 321], [523, 322], [524, 321], [525, 323], [526, 324], [527, 321], [528, 321], [529, 325], [530, 324], [531, 326], [532, 327], [533, 319], [534, 319], [537, 319], [535, 319], [536, 324], [538, 206], [539, 324], [540, 328], [541, 324], [542, 324], [543, 317], [544, 317], [545, 324], [546, 317], [548, 329], [553, 330], [550, 331], [551, 332], [554, 332], [552, 332], [424, 330], [555, 324], [556, 206], [493, 333], [557, 334], [558, 328], [559, 335], [560, 324], [561, 336], [562, 335], [563, 336], [564, 324], [640, 337], [641, 338], [642, 339], [643, 340], [984, 341], [985, 342], [644, 343], [645, 344], [971, 345], [986, 346], [987, 347], [972, 348], [973, 349], [974, 350], [975, 351], [976, 352], [977, 353], [978, 354], [979, 349], [980, 355], [988, 356], [981, 357], [982, 358], [983, 359], [522, 360], [517, 361], [518, 5], [570, 362], [519, 363], [566, 364], [568, 363], [586, 365], [577, 363], [580, 363], [549, 362], [582, 363], [571, 362], [575, 364], [565, 366], [567, 367], [569, 368], [584, 369], [578, 370], [581, 371], [579, 372], [583, 373], [585, 361], [576, 374], [420, 5], [587, 206], [521, 375], [498, 376], [500, 377], [499, 378], [417, 5], [494, 379], [588, 380]], "semanticDiagnosticsPerFile": [[422, [{"start": 7230, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'startedAt' does not exist on type 'Partial<LessonProgress>'."}, {"start": 7260, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'startedAt' does not exist on type 'Partial<LessonProgress>'."}, {"start": 7428, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'answers' does not exist on type 'Partial<LessonProgress>'."}, {"start": 7447, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'answers' does not exist on type 'Partial<LessonProgress>'."}, {"start": 7878, "length": 9, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'startedAt' does not exist in type 'LessonProgress'."}, {"start": 8361, "length": 9, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'startedAt' does not exist in type 'LessonProgress'."}, {"start": 9005, "length": 9, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'startedAt' does not exist in type 'LessonProgress | LessonProgress[]'."}]], [503, [{"start": 1230, "length": 1, "messageText": "Parameter 'l' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1827, "length": 6, "messageText": "Parameter 'lesson' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [511, [{"start": 2561, "length": 3, "messageText": "Parameter 'msg' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [519, [{"start": 2771, "length": 94, "messageText": "Expected 1-2 arguments, but got 3.", "category": 1, "code": 2554}]], [525, [{"start": 2876, "length": 31, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'string[] | undefined' to type 'string' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'string[]' is not comparable to type 'string'.", "category": 1, "code": 2678}]}}, {"start": 2925, "length": 24, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'string[] | undefined' to type 'string' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'string[]' is not comparable to type 'string'.", "category": 1, "code": 2678}]}}]], [530, [{"start": 16748, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'moduleId' does not exist in type 'ExamModule | ExamModule[]'.", "relatedInformation": [{"file": "./src/types/api.ts", "start": 3052, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type 'ApiResponse<ExamModule | ExamModule[]>'", "category": 3, "code": 6500}]}]], [532, [{"start": 107, "length": 16, "messageText": "Cannot find module '@/utils/mockDb' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 839, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 925, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1011, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [536, [{"start": 12472, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'results' does not exist in type 'VerbConjugationExercise | VerbConjugationExercise[]'.", "relatedInformation": [{"file": "./src/types/api.ts", "start": 3052, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type 'ApiResponse<VerbConjugationExercise | VerbConjugationExercise[]>'", "category": 3, "code": 6500}]}]], [541, [{"start": 1139, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1931, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2690, "length": 1, "messageText": "Parameter 'l' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3208, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3414, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3640, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5205, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5411, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5587, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [543, [{"start": 1018, "length": 6, "messageText": "Parameter 'lesson' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1056, "length": 1, "messageText": "Parameter 't' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1205, "length": 6, "messageText": "Parameter 'lesson' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [544, [{"start": 1178, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [546, [{"start": 1803, "length": 7, "messageText": "Parameter 'session' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1874, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [548, [{"start": 1637, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1840, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [550, [{"start": 1397, "length": 25, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type 'string[] | undefined' to type 'string' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type 'string[]' is not comparable to type 'string'.", "category": 1, "code": 2678}]}}]], [551, [{"start": 476, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 735, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 987, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 1325, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 1670, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 1996, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 2369, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 2737, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 3110, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}]], [554, [{"start": 439, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 698, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 950, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 1288, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 1633, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 1959, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 2332, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 2700, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}, {"start": 3073, "length": 8, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'audioUrl' does not exist in type 'PronunciationPhrase'."}]], [555, [{"start": 1561, "length": 8, "code": 2322, "category": 1, "messageText": "Type '\"shopping\"' is not assignable to type '\"travel\" | \"dining\" | \"greetings\" | \"everyday\" | \"business\" | undefined'.", "relatedInformation": [{"start": 377, "length": 8, "messageText": "The expected type comes from property 'category' which is declared here on type 'SpeakingExercise'", "category": 3, "code": 6500}]}, {"start": 2919, "length": 8, "code": 2322, "category": 1, "messageText": "Type '\"shopping\"' is not assignable to type '\"travel\" | \"dining\" | \"greetings\" | \"everyday\" | \"business\" | undefined'.", "relatedInformation": [{"start": 377, "length": 8, "messageText": "The expected type comes from property 'category' which is declared here on type 'SpeakingExercise'", "category": 3, "code": 6500}]}, {"start": 4517, "length": 8, "code": 2322, "category": 1, "messageText": "Type '\"shopping\"' is not assignable to type '\"travel\" | \"dining\" | \"greetings\" | \"everyday\" | \"business\" | undefined'.", "relatedInformation": [{"start": 377, "length": 8, "messageText": "The expected type comes from property 'category' which is declared here on type 'SpeakingExercise'", "category": 3, "code": 6500}]}, {"start": 8268, "length": 4, "code": 2322, "category": 1, "messageText": "Type '{ accuracy: number; pronunciation: number; fluency: number; feedback: string; type: string; }' is not assignable to type 'SpeakingExercise | SpeakingExercise[] | undefined'.", "relatedInformation": [{"file": "./src/types/api.ts", "start": 3052, "length": 4, "messageText": "The expected type comes from property 'data' which is declared here on type 'ApiResponse<SpeakingExercise | SpeakingExercise[]>'", "category": 3, "code": 6500}]}]], [561, [{"start": 691, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [562, [{"start": 1252, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1433, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1771, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [563, [{"start": 680, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [564, [{"start": 1049, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [568, [{"start": 1352, "length": 6, "code": 2741, "category": 1, "messageText": "Property 'success' is missing in type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/services/api/apiClient\").ApiResponse<GrammarExerciseListResponse>' but required in type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/types/api\").ApiResponse<GrammarExerciseListResponse>'.", "relatedInformation": [{"file": "./src/types/api.ts", "start": 3032, "length": 7, "messageText": "'success' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/services/api/apiClient\").ApiResponse<GrammarExerciseListResponse>' is not assignable to type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/types/api\").ApiResponse<GrammarExerciseListResponse>'."}}, {"start": 1568, "length": 6, "code": 2741, "category": 1, "messageText": "Property 'success' is missing in type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/services/api/apiClient\").ApiResponse<GrammarExercise>' but required in type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/types/api\").ApiResponse<GrammarExercise>'.", "relatedInformation": [{"file": "./src/types/api.ts", "start": 3032, "length": 7, "messageText": "'success' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/services/api/apiClient\").ApiResponse<GrammarExercise>' is not assignable to type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/types/api\").ApiResponse<GrammarExercise>'."}}, {"start": 1749, "length": 6, "code": 2741, "category": 1, "messageText": "Property 'success' is missing in type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/services/api/apiClient\").ApiResponse<any[]>' but required in type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/types/api\").ApiResponse<any[]>'.", "relatedInformation": [{"file": "./src/types/api.ts", "start": 3032, "length": 7, "messageText": "'success' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/services/api/apiClient\").ApiResponse<any[]>' is not assignable to type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/types/api\").ApiResponse<any[]>'."}}, {"start": 1945, "length": 6, "code": 2741, "category": 1, "messageText": "Property 'success' is missing in type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/services/api/apiClient\").ApiResponse<any>' but required in type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/types/api\").ApiResponse<any>'.", "relatedInformation": [{"file": "./src/types/api.ts", "start": 3032, "length": 7, "messageText": "'success' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/services/api/apiClient\").ApiResponse<any>' is not assignable to type 'import(\"/Users/<USER>/Development/personal/french-lesson/src/types/api\").ApiResponse<any>'."}}]], [575, [{"start": 3507, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(string | undefined)[]' is not assignable to type 'string[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string | undefined' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}}]], [582, [{"start": 2392, "length": 94, "messageText": "Expected 1-2 arguments, but got 3.", "category": 1, "code": 2554}]], [588, [{"start": 2170, "length": 80, "code": 2345, "category": 1, "messageText": "Argument of type '{ level: any; category: any; }' is not assignable to parameter of type 'string'."}]], [978, [{"start": 6778, "length": 8, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ phrase: string; translation: string; audioUrl: any; onResult: (result: PronunciationResult) => void; }' is not assignable to type 'IntrinsicAttributes & PronunciationPracticeProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'audioUrl' does not exist on type 'IntrinsicAttributes & PronunciationPracticeProps'.", "category": 1, "code": 2339}]}}, {"start": 6795, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'audioUrl' does not exist on type 'PronunciationPhrase'."}]]], "affectedFilesPendingEmit": [410, 411, 415, 414, 413, 412, 591, 592, 593, 595, 596, 594, 597, 598, 599, 600, 601, 602, 603, 604, 606, 605, 607, 608, 609, 610, 611, 612, 574, 613, 614, 615, 616, 618, 619, 620, 621, 622, 627, 628, 572, 573, 629, 630, 617, 631, 632, 633, 590, 625, 634, 623, 635, 589, 418, 422, 419, 416, 639, 501, 502, 503, 510, 511, 512, 513, 514, 515, 423, 520, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 537, 535, 536, 538, 539, 540, 541, 542, 543, 544, 545, 546, 548, 553, 550, 551, 554, 552, 424, 555, 556, 493, 557, 558, 559, 560, 561, 562, 563, 564, 640, 641, 642, 643, 984, 985, 644, 645, 971, 986, 987, 972, 973, 974, 975, 976, 977, 978, 979, 980, 988, 981, 982, 983, 522, 517, 518, 570, 519, 566, 568, 586, 577, 580, 549, 582, 571, 575, 565, 567, 569, 584, 578, 581, 579, 583, 585, 576, 420, 587, 521, 498, 500, 499, 417, 494, 588], "version": "5.8.3"}